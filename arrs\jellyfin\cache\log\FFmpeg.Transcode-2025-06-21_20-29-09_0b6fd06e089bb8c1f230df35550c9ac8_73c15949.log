{"Protocol":0,"Id":"0b6fd06e089bb8c1f230df35550c9ac8","Path":"/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":38242705853,"Name":"How to Train Your Dragon (2010) Remux-2160p","IsRemote":false,"ETag":"c48facbf023e8a2b5e49a9f09647c736","RunTimeTicks":58907520000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"hevc","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt2020nc","ColorTransfer":"smpte2084","ColorPrimaries":"bt2020","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":2,"VideoRangeType":2,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"4K HEVC HDR","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":51935923,"BitDepth":10,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main 10","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p10le","Level":153,"IsAnamorphic":false},{"Codec":"dts","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - DTS - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":768000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"ac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"dts","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":2,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - DTS-HD MA \u002B DTS:X - 7.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"7.1","BitRate":5553891,"BitDepth":24,"RefFrames":null,"PacketLength":null,"Channels":8,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS-HD MA \u002B DTS:X","Type":0,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Forzados","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Forzados - Spanish - Predeterminado - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"PGSSUB","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - PGSSUB","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":6,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":58897814,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:40:00.000  -i file:"/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 19232000 -bufsize 38464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 "expr:gte(t,n_forced*3)" -sc_threshold:v:0 0 -vf "setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,2560)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 800 -hls_segment_filename "/cache/transcodes/762c645940747f901ec70a0e5555697c%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/762c645940747f901ec70a0e5555697c.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, matroska,webm, from 'file:/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv':
  Metadata:
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2019-01-10T20:42:53.000000Z
  Duration: 01:38:10.75, start: 0.000000, bitrate: 51935 kb/s
  Chapters:
    Chapter #0:0: start 0.000000, end 543.793244
      Metadata:
        title           : Chapter 01
    Chapter #0:1: start 543.793244, end 716.006956
      Metadata:
        title           : Chapter 02
    Chapter #0:2: start 716.006956, end 935.059111
      Metadata:
        title           : Chapter 03
    Chapter #0:3: start 935.059111, end 1341.089733
      Metadata:
        title           : Chapter 04
    Chapter #0:4: start 1341.089733, end 1685.725689
      Metadata:
        title           : Chapter 05
    Chapter #0:5: start 1685.725689, end 2018.224533
      Metadata:
        title           : Chapter 06
    Chapter #0:6: start 2018.224533, end 2371.160444
      Metadata:
        title           : Chapter 07
    Chapter #0:7: start 2371.160444, end 2632.713400
      Metadata:
        title           : Chapter 08
    Chapter #0:8: start 2632.713400, end 2852.391200
      Metadata:
        title           : Chapter 09
    Chapter #0:9: start 2852.391200, end 3011.216533
      Metadata:
        title           : Chapter 10
    Chapter #0:10: start 3011.216533, end 3619.824533
      Metadata:
        title           : Chapter 11
    Chapter #0:11: start 3619.824533, end 3911.407489
      Metadata:
        title           : Chapter 12
    Chapter #0:12: start 3911.407489, end 4192.188000
      Metadata:
        title           : Chapter 13
    Chapter #0:13: start 4192.188000, end 4382.211156
      Metadata:
        title           : Chapter 14
    Chapter #0:14: start 4382.211156, end 5030.066689
      Metadata:
        title           : Chapter 15
    Chapter #0:15: start 5030.066689, end 5358.519822
      Metadata:
        title           : Chapter 16
    Chapter #0:16: start 5358.519822, end 5890.752000
      Metadata:
        title           : Chapter 17
  Stream #0:0: Video: hevc (Main 10), yuv420p10le(tv, bt2020nc/bt2020/smpte2084), 3840x2160 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 44932856
        DURATION-eng    : 01:38:10.718000000
        NUMBER_OF_FRAMES-eng: 141236
        NUMBER_OF_BYTES-eng: 33085848639
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: dts (dca) (DTS), 48000 Hz, 5.1(side), fltp, 768 kb/s (default) (forced)
      Metadata:
        BPS-eng         : 768000
        DURATION-eng    : 01:38:10.720000000
        NUMBER_OF_FRAMES-eng: 552255
        NUMBER_OF_BYTES-eng: 565509120
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(spa): Audio: ac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        BPS-eng         : 640000
        DURATION-eng    : 01:38:10.752000000
        NUMBER_OF_FRAMES-eng: 184086
        NUMBER_OF_BYTES-eng: 471260160
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(eng): Audio: dts (dca) (DTS-HD MA + DTS:X), 48000 Hz, 7.1, s32p (24 bit)
      Metadata:
        BPS-eng         : 5553891
        DURATION-eng    : 01:38:10.720000000
        NUMBER_OF_FRAMES-eng: 552255
        NUMBER_OF_BYTES-eng: 4089552576
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(spa): Subtitle: subrip (srt) (default) (forced)
      Metadata:
        title           : Forzados
        BPS-eng         : 0
        DURATION-eng    : 00:08:47.818000000
        NUMBER_OF_FRAMES-eng: 2
        NUMBER_OF_BYTES-eng: 44
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:5(spa): Subtitle: subrip (srt)
      Metadata:
        BPS-eng         : 42
        DURATION-eng    : 01:37:08.113000000
        NUMBER_OF_FRAMES-eng: 870
        NUMBER_OF_BYTES-eng: 30771
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:6(eng): Subtitle: hdmv_pgs_subtitle (pgssub), 1920x1080
      Metadata:
        BPS-eng         : 39065
        DURATION-eng    : 01:30:28.715000000
        NUMBER_OF_FRAMES-eng: 2395
        NUMBER_OF_BYTES-eng: 26509155
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
[tonemapx @ 0x560752cbd580] Using CPU capabilities: AVX2 FMA3
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (dts (dca) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[tonemapx @ 0x7f86a4012140] Using CPU capabilities: AVX2 FMA3
[libx264 @ 0x56074db21b80] using SAR=1/1
[libx264 @ 0x56074db21b80] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x56074db21b80] profile High, level 5.0, 4:2:0, 8-bit
[libx264 @ 0x56074db21b80] 264 - core 164 r3095 baee400 - H.264/MPEG-4 AVC codec - Copyleft 2003-2022 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=0 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1 trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=18 lookahead_threads=3 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=23 scenecut=0 intra_refresh=0 rc_lookahead=10 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 vbv_maxrate=19232 vbv_bufsize=38464 crf_max=0.0 nal_hrd=none filler=0 ip_ratio=1.40 aq=1:1.00
Output #0, hls, to '/cache/transcodes/762c645940747f901ec70a0e5555697c.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264, yuv420p(tv, bt709, progressive), 2560x1440 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 90k tbn (default)
      Metadata:
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 19232000/0/0 buffer size: 38464000 vbv_delay: N/A
  Stream #0:1: Audio: aac, 48000 Hz, 5.1, s16, 640 kb/s (default) (forced)
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=   11 fps=5.5 q=28.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   27 fps= 11 q=28.0 size=N/A time=00:00:00.66 bitrate=N/A speed=0.267x    
frame=   40 fps= 13 q=28.0 size=N/A time=00:00:01.20 bitrate=N/A speed=0.402x    
frame=   54 fps= 15 q=28.0 size=N/A time=00:00:01.79 bitrate=N/A speed=0.511x    
frame=   66 fps= 16 q=28.0 size=N/A time=00:00:02.29 bitrate=N/A speed=0.572x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c800.ts' for writing
frame=   84 fps= 19 q=29.0 size=N/A time=00:00:03.04 bitrate=N/A speed=0.674x    
frame=  104 fps= 21 q=29.0 size=N/A time=00:00:03.87 bitrate=N/A speed=0.773x    
frame=  127 fps= 23 q=28.0 size=N/A time=00:00:04.83 bitrate=N/A speed=0.877x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c801.ts' for writing
frame=  145 fps= 24 q=28.0 size=N/A time=00:00:05.58 bitrate=N/A speed=0.929x    
frame=  168 fps= 26 q=28.0 size=N/A time=00:00:06.54 bitrate=N/A speed=   1x    
frame=  191 fps= 27 q=28.0 size=N/A time=00:00:07.50 bitrate=N/A speed=1.07x    
frame=  215 fps= 29 q=28.0 size=N/A time=00:00:08.50 bitrate=N/A speed=1.13x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c802.ts' for writing
frame=  237 fps= 30 q=28.0 size=N/A time=00:00:09.42 bitrate=N/A speed=1.18x    
frame=  263 fps= 31 q=28.0 size=N/A time=00:00:10.51 bitrate=N/A speed=1.23x    
frame=  283 fps= 31 q=28.0 size=N/A time=00:00:11.34 bitrate=N/A speed=1.26x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c803.ts' for writing
frame=  303 fps= 32 q=28.0 size=N/A time=00:00:12.17 bitrate=N/A speed=1.28x    
frame=  327 fps= 33 q=28.0 size=N/A time=00:00:13.17 bitrate=N/A speed=1.32x    
frame=  347 fps= 33 q=28.0 size=N/A time=00:00:14.01 bitrate=N/A speed=1.33x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c804.ts' for writing
frame=  364 fps= 33 q=28.0 size=N/A time=00:00:14.72 bitrate=N/A speed=1.34x    
frame=  386 fps= 33 q=28.0 size=N/A time=00:00:15.64 bitrate=N/A speed=1.36x    
frame=  407 fps= 34 q=28.0 size=N/A time=00:00:16.51 bitrate=N/A speed=1.37x    
frame=  426 fps= 34 q=28.0 size=N/A time=00:00:17.30 bitrate=N/A speed=1.38x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c805.ts' for writing
frame=  445 fps= 34 q=28.0 size=N/A time=00:00:18.10 bitrate=N/A speed=1.39x    
frame=  465 fps= 34 q=28.0 size=N/A time=00:00:18.93 bitrate=N/A speed= 1.4x    
frame=  486 fps= 35 q=28.0 size=N/A time=00:00:19.85 bitrate=N/A speed=1.42x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c806.ts' for writing
frame=  511 fps= 35 q=28.0 size=N/A time=00:00:20.85 bitrate=N/A speed=1.44x    
frame=  531 fps= 35 q=28.0 size=N/A time=00:00:21.68 bitrate=N/A speed=1.44x    
frame=  550 fps= 35 q=28.0 size=N/A time=00:00:22.48 bitrate=N/A speed=1.45x    
frame=  568 fps= 35 q=28.0 size=N/A time=00:00:23.27 bitrate=N/A speed=1.45x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c807.ts' for writing
frame=  588 fps= 36 q=28.0 size=N/A time=00:00:24.06 bitrate=N/A speed=1.46x    
frame=  609 fps= 36 q=28.0 size=N/A time=00:00:24.94 bitrate=N/A speed=1.46x    
frame=  630 fps= 36 q=28.0 size=N/A time=00:00:25.81 bitrate=N/A speed=1.47x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c808.ts' for writing
frame=  652 fps= 36 q=28.0 size=N/A time=00:00:26.73 bitrate=N/A speed=1.48x    
frame=  674 fps= 36 q=28.0 size=N/A time=00:00:27.65 bitrate=N/A speed=1.49x    
frame=  700 fps= 37 q=28.0 size=N/A time=00:00:28.73 bitrate=N/A speed=1.51x    
frame=  720 fps= 37 q=28.0 size=N/A time=00:00:29.57 bitrate=N/A speed=1.51x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c809.ts' for writing
frame=  736 fps= 37 q=28.0 size=N/A time=00:00:30.23 bitrate=N/A speed=1.51x    
frame=  754 fps= 37 q=28.0 size=N/A time=00:00:30.98 bitrate=N/A speed=1.51x    
frame=  772 fps= 37 q=28.0 size=N/A time=00:00:31.74 bitrate=N/A speed=1.51x    
frame=  788 fps= 37 q=28.0 size=N/A time=00:00:32.40 bitrate=N/A speed=1.51x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c810.ts' for writing
frame=  805 fps= 37 q=28.0 size=N/A time=00:00:33.11 bitrate=N/A speed= 1.5x    
frame=  822 fps= 36 q=28.0 size=N/A time=00:00:33.82 bitrate=N/A speed= 1.5x    
frame=  838 fps= 36 q=28.0 size=N/A time=00:00:34.49 bitrate=N/A speed= 1.5x    
frame=  855 fps= 36 q=28.0 size=N/A time=00:00:35.20 bitrate=N/A speed= 1.5x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c811.ts' for writing
frame=  875 fps= 36 q=28.0 size=N/A time=00:00:36.03 bitrate=N/A speed= 1.5x    
frame=  893 fps= 36 q=28.0 size=N/A time=00:00:36.78 bitrate=N/A speed= 1.5x    
frame=  915 fps= 37 q=28.0 size=N/A time=00:00:37.70 bitrate=N/A speed=1.51x    
frame=  935 fps= 37 q=28.0 size=N/A time=00:00:38.53 bitrate=N/A speed=1.51x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c812.ts' for writing
frame=  955 fps= 37 q=28.0 size=N/A time=00:00:39.37 bitrate=N/A speed=1.51x    
frame=  974 fps= 37 q=28.0 size=N/A time=00:00:40.20 bitrate=N/A speed=1.51x    
frame=  994 fps= 37 q=28.0 size=N/A time=00:00:40.99 bitrate=N/A speed=1.52x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c813.ts' for writing
frame= 1009 fps= 37 q=28.0 size=N/A time=00:00:41.62 bitrate=N/A speed=1.51x    
frame= 1030 fps= 37 q=28.0 size=N/A time=00:00:42.50 bitrate=N/A speed=1.52x    
frame= 1052 fps= 37 q=28.0 size=N/A time=00:00:43.41 bitrate=N/A speed=1.52x    
frame= 1075 fps= 37 q=28.0 size=N/A time=00:00:44.37 bitrate=N/A speed=1.53x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c814.ts' for writing
frame= 1095 fps= 37 q=28.0 size=N/A time=00:00:45.21 bitrate=N/A speed=1.53x    
frame= 1114 fps= 37 q=28.0 size=N/A time=00:00:46.00 bitrate=N/A speed=1.53x    
frame= 1134 fps= 37 q=28.0 size=N/A time=00:00:46.83 bitrate=N/A speed=1.53x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c815.ts' for writing
frame= 1153 fps= 37 q=28.0 size=N/A time=00:00:47.67 bitrate=N/A speed=1.54x    
frame= 1178 fps= 37 q=28.0 size=N/A time=00:00:48.67 bitrate=N/A speed=1.54x    
frame= 1198 fps= 37 q=28.0 size=N/A time=00:00:49.50 bitrate=N/A speed=1.54x    
frame= 1218 fps= 37 q=28.0 size=N/A time=00:00:50.34 bitrate=N/A speed=1.55x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c816.ts' for writing
frame= 1239 fps= 37 q=28.0 size=N/A time=00:00:51.21 bitrate=N/A speed=1.55x    
frame= 1263 fps= 38 q=28.0 size=N/A time=00:00:52.21 bitrate=N/A speed=1.56x    
frame= 1283 fps= 38 q=28.0 size=N/A time=00:00:53.05 bitrate=N/A speed=1.56x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c817.ts' for writing
frame= 1304 fps= 38 q=28.0 size=N/A time=00:00:53.92 bitrate=N/A speed=1.56x    
frame= 1328 fps= 38 q=28.0 size=N/A time=00:00:54.92 bitrate=N/A speed=1.57x    
frame= 1351 fps= 38 q=25.0 size=N/A time=00:00:55.88 bitrate=N/A speed=1.57x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c818.ts' for writing
frame= 1373 fps= 38 q=28.0 size=N/A time=00:00:56.80 bitrate=N/A speed=1.58x    
frame= 1394 fps= 38 q=28.0 size=N/A time=00:00:57.68 bitrate=N/A speed=1.58x    
frame= 1415 fps= 38 q=28.0 size=N/A time=00:00:58.55 bitrate=N/A speed=1.58x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c819.ts' for writing
frame= 1442 fps= 38 q=28.0 size=N/A time=00:00:59.68 bitrate=N/A speed=1.59x    
frame= 1462 fps= 38 q=28.0 size=N/A time=00:01:00.51 bitrate=N/A speed=1.59x    
frame= 1481 fps= 38 q=28.0 size=N/A time=00:01:01.31 bitrate=N/A speed=1.59x    
frame= 1504 fps= 39 q=28.0 size=N/A time=00:01:02.27 bitrate=N/A speed=1.59x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c820.ts' for writing
frame= 1524 fps= 39 q=28.0 size=N/A time=00:01:03.10 bitrate=N/A speed= 1.6x    
frame= 1548 fps= 39 q=28.0 size=N/A time=00:01:04.10 bitrate=N/A speed= 1.6x    
frame= 1567 fps= 39 q=25.0 size=N/A time=00:01:04.89 bitrate=N/A speed= 1.6x    
frame= 1581 fps= 39 q=28.0 size=N/A time=00:01:05.48 bitrate=N/A speed=1.59x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c821.ts' for writing
frame= 1606 fps= 39 q=28.0 size=N/A time=00:01:06.52 bitrate=N/A speed= 1.6x    
frame= 1629 fps= 39 q=28.0 size=N/A time=00:01:07.48 bitrate=N/A speed= 1.6x    
frame= 1651 fps= 39 q=28.0 size=N/A time=00:01:08.40 bitrate=N/A speed=1.61x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c822.ts' for writing
frame= 1673 fps= 39 q=28.0 size=N/A time=00:01:09.31 bitrate=N/A speed=1.61x    
frame= 1691 fps= 39 q=28.0 size=N/A time=00:01:10.07 bitrate=N/A speed=1.61x    
frame= 1712 fps= 39 q=28.0 size=N/A time=00:01:10.94 bitrate=N/A speed=1.61x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c823.ts' for writing
frame= 1735 fps= 39 q=28.0 size=N/A time=00:01:11.90 bitrate=N/A speed=1.61x    
frame= 1756 fps= 39 q=28.0 size=N/A time=00:01:12.78 bitrate=N/A speed=1.62x    
frame= 1776 fps= 39 q=28.0 size=N/A time=00:01:13.61 bitrate=N/A speed=1.62x    
frame= 1794 fps= 39 q=28.0 size=N/A time=00:01:14.36 bitrate=N/A speed=1.61x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c824.ts' for writing
frame= 1815 fps= 39 q=28.0 size=N/A time=00:01:15.24 bitrate=N/A speed=1.62x    
frame= 1836 fps= 39 q=28.0 size=N/A time=00:01:16.11 bitrate=N/A speed=1.62x    
frame= 1860 fps= 39 q=28.0 size=N/A time=00:01:17.11 bitrate=N/A speed=1.62x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c825.ts' for writing
frame= 1881 fps= 39 q=28.0 size=N/A time=00:01:17.99 bitrate=N/A speed=1.62x    
frame= 1904 fps= 39 q=28.0 size=N/A time=00:01:18.95 bitrate=N/A speed=1.63x    
frame= 1927 fps= 39 q=25.0 size=N/A time=00:01:19.91 bitrate=N/A speed=1.63x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c826.ts' for writing
frame= 1951 fps= 39 q=28.0 size=N/A time=00:01:20.91 bitrate=N/A speed=1.63x    
frame= 1974 fps= 39 q=28.0 size=N/A time=00:01:21.87 bitrate=N/A speed=1.64x    
frame= 1992 fps= 39 q=28.0 size=N/A time=00:01:22.62 bitrate=N/A speed=1.63x    
frame= 2010 fps= 39 q=28.0 size=N/A time=00:01:23.37 bitrate=N/A speed=1.63x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c827.ts' for writing
frame= 2035 fps= 39 q=28.0 size=N/A time=00:01:24.41 bitrate=N/A speed=1.64x    
frame= 2059 fps= 40 q=28.0 size=N/A time=00:01:25.41 bitrate=N/A speed=1.64x    
frame= 2083 fps= 40 q=28.0 size=N/A time=00:01:26.41 bitrate=N/A speed=1.64x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c828.ts' for writing
frame= 2103 fps= 40 q=28.0 size=N/A time=00:01:27.25 bitrate=N/A speed=1.64x    
frame= 2125 fps= 40 q=28.0 size=N/A time=00:01:28.17 bitrate=N/A speed=1.65x    
frame= 2153 fps= 40 q=28.0 size=N/A time=00:01:29.33 bitrate=N/A speed=1.65x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c829.ts' for writing
frame= 2177 fps= 40 q=28.0 size=N/A time=00:01:30.34 bitrate=N/A speed=1.66x    
frame= 2199 fps= 40 q=28.0 size=N/A time=00:01:31.25 bitrate=N/A speed=1.66x    
frame= 2218 fps= 40 q=28.0 size=N/A time=00:01:32.05 bitrate=N/A speed=1.66x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c830.ts' for writing
frame= 2240 fps= 40 q=28.0 size=N/A time=00:01:32.96 bitrate=N/A speed=1.66x    
frame= 2264 fps= 40 q=28.0 size=N/A time=00:01:33.96 bitrate=N/A speed=1.66x    
frame= 2289 fps= 40 q=28.0 size=N/A time=00:01:35.01 bitrate=N/A speed=1.66x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c831.ts' for writing
frame= 2315 fps= 40 q=28.0 size=N/A time=00:01:36.09 bitrate=N/A speed=1.67x    
frame= 2340 fps= 40 q=28.0 size=N/A time=00:01:37.13 bitrate=N/A speed=1.67x    
frame= 2364 fps= 40 q=28.0 size=N/A time=00:01:38.13 bitrate=N/A speed=1.68x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c832.ts' for writing
frame= 2390 fps= 40 q=28.0 size=N/A time=00:01:39.22 bitrate=N/A speed=1.68x    
frame= 2414 fps= 41 q=28.0 size=N/A time=00:01:40.22 bitrate=N/A speed=1.68x    
frame= 2433 fps= 40 q=28.0 size=N/A time=00:01:41.01 bitrate=N/A speed=1.68x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c833.ts' for writing
frame= 2457 fps= 41 q=28.0 size=N/A time=00:01:42.01 bitrate=N/A speed=1.68x    
frame= 2479 fps= 41 q=28.0 size=N/A time=00:01:42.93 bitrate=N/A speed=1.69x    
frame= 2503 fps= 41 q=28.0 size=N/A time=00:01:43.97 bitrate=N/A speed=1.69x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c834.ts' for writing
frame= 2525 fps= 41 q=28.0 size=N/A time=00:01:44.85 bitrate=N/A speed=1.69x    
frame= 2549 fps= 41 q=28.0 size=N/A time=00:01:45.85 bitrate=N/A speed=1.69x    
frame= 2564 fps= 41 q=28.0 size=N/A time=00:01:46.48 bitrate=N/A speed=1.69x    
frame= 2581 fps= 41 q=28.0 size=N/A time=00:01:47.19 bitrate=N/A speed=1.69x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c835.ts' for writing
frame= 2606 fps= 41 q=28.0 size=N/A time=00:01:48.23 bitrate=N/A speed=1.69x    
frame= 2630 fps= 41 q=28.0 size=N/A time=00:01:49.23 bitrate=N/A speed=1.69x    
frame= 2656 fps= 41 q=28.0 size=N/A time=00:01:50.31 bitrate=N/A speed=1.69x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c836.ts' for writing
frame= 2678 fps= 41 q=28.0 size=N/A time=00:01:51.23 bitrate=N/A speed= 1.7x    
frame= 2701 fps= 41 q=28.0 size=N/A time=00:01:52.19 bitrate=N/A speed= 1.7x    
frame= 2724 fps= 41 q=28.0 size=N/A time=00:01:53.15 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c837.ts' for writing
frame= 2747 fps= 41 q=28.0 size=N/A time=00:01:54.15 bitrate=N/A speed= 1.7x    
frame= 2770 fps= 41 q=28.0 size=N/A time=00:01:55.07 bitrate=N/A speed= 1.7x    
frame= 2794 fps= 41 q=28.0 size=N/A time=00:01:56.07 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c838.ts' for writing
frame= 2818 fps= 41 q=28.0 size=N/A time=00:01:57.07 bitrate=N/A speed=1.71x    
frame= 2837 fps= 41 q=28.0 size=N/A time=00:01:57.86 bitrate=N/A speed=1.71x    
frame= 2855 fps= 41 q=28.0 size=N/A time=00:01:58.61 bitrate=N/A speed= 1.7x    
frame= 2874 fps= 41 q=28.0 size=N/A time=00:01:59.41 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c839.ts' for writing
frame= 2895 fps= 41 q=28.0 size=N/A time=00:02:00.28 bitrate=N/A speed= 1.7x    
frame= 2915 fps= 41 q=28.0 size=N/A time=00:02:01.12 bitrate=N/A speed= 1.7x    
frame= 2934 fps= 41 q=25.0 size=N/A time=00:02:01.91 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c840.ts' for writing
frame= 2955 fps= 41 q=28.0 size=N/A time=00:02:02.78 bitrate=N/A speed= 1.7x    
frame= 2972 fps= 41 q=28.0 size=N/A time=00:02:03.49 bitrate=N/A speed= 1.7x    
frame= 2990 fps= 41 q=28.0 size=N/A time=00:02:04.24 bitrate=N/A speed= 1.7x    
frame= 3007 fps= 41 q=28.0 size=N/A time=00:02:04.95 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c841.ts' for writing
frame= 3030 fps= 41 q=28.0 size=N/A time=00:02:05.91 bitrate=N/A speed= 1.7x    
frame= 3049 fps= 41 q=28.0 size=N/A time=00:02:06.70 bitrate=N/A speed= 1.7x    
frame= 3072 fps= 41 q=28.0 size=N/A time=00:02:07.66 bitrate=N/A speed= 1.7x    
frame= 3092 fps= 41 q=28.0 size=N/A time=00:02:08.50 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c842.ts' for writing
frame= 3114 fps= 41 q=28.0 size=N/A time=00:02:09.42 bitrate=N/A speed= 1.7x    
frame= 3138 fps= 41 q=28.0 size=N/A time=00:02:10.42 bitrate=N/A speed= 1.7x    
frame= 3159 fps= 41 q=28.0 size=N/A time=00:02:11.29 bitrate=N/A speed= 1.7x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c843.ts' for writing
frame= 3180 fps= 41 q=28.0 size=N/A time=00:02:12.17 bitrate=N/A speed= 1.7x    
frame= 3202 fps= 41 q=28.0 size=N/A time=00:02:13.09 bitrate=N/A speed= 1.7x    
frame= 3228 fps= 41 q=28.0 size=N/A time=00:02:14.17 bitrate=N/A speed=1.71x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c844.ts' for writing
frame= 3251 fps= 41 q=28.0 size=N/A time=00:02:15.13 bitrate=N/A speed=1.71x    
frame= 3275 fps= 41 q=28.0 size=N/A time=00:02:16.13 bitrate=N/A speed=1.71x    
frame= 3299 fps= 41 q=28.0 size=N/A time=00:02:17.13 bitrate=N/A speed=1.71x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c845.ts' for writing
frame= 3323 fps= 41 q=28.0 size=N/A time=00:02:18.13 bitrate=N/A speed=1.71x    
frame= 3347 fps= 41 q=28.0 size=N/A time=00:02:19.13 bitrate=N/A speed=1.72x    
frame= 3376 fps= 41 q=28.0 size=N/A time=00:02:20.34 bitrate=N/A speed=1.72x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c846.ts' for writing
frame= 3405 fps= 41 q=28.0 size=N/A time=00:02:21.55 bitrate=N/A speed=1.72x    
frame= 3433 fps= 42 q=28.0 size=N/A time=00:02:22.72 bitrate=N/A speed=1.73x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c847.ts' for writing
frame= 3459 fps= 42 q=28.0 size=N/A time=00:02:23.81 bitrate=N/A speed=1.73x    
frame= 3484 fps= 42 q=28.0 size=N/A time=00:02:24.85 bitrate=N/A speed=1.73x    
frame= 3513 fps= 42 q=28.0 size=N/A time=00:02:26.06 bitrate=N/A speed=1.74x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c848.ts' for writing
frame= 3538 fps= 42 q=28.0 size=N/A time=00:02:27.10 bitrate=N/A speed=1.74x    
frame= 3564 fps= 42 q=28.0 size=N/A time=00:02:28.18 bitrate=N/A speed=1.74x    
frame= 3592 fps= 42 q=28.0 size=N/A time=00:02:29.35 bitrate=N/A speed=1.74x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c849.ts' for writing
frame= 3622 fps= 42 q=28.0 size=N/A time=00:02:30.60 bitrate=N/A speed=1.75x    
frame= 3651 fps= 42 q=28.0 size=N/A time=00:02:31.81 bitrate=N/A speed=1.75x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c850.ts' for writing
frame= 3683 fps= 42 q=28.0 size=N/A time=00:02:33.15 bitrate=N/A speed=1.76x    
frame= 3711 fps= 42 q=28.0 size=N/A time=00:02:34.32 bitrate=N/A speed=1.76x    
frame= 3737 fps= 42 q=28.0 size=N/A time=00:02:35.40 bitrate=N/A speed=1.76x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c851.ts' for writing
frame= 3765 fps= 42 q=28.0 size=N/A time=00:02:36.57 bitrate=N/A speed=1.77x    
frame= 3796 fps= 43 q=28.0 size=N/A time=00:02:37.86 bitrate=N/A speed=1.77x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c852.ts' for writing
frame= 3824 fps= 43 q=28.0 size=N/A time=00:02:39.03 bitrate=N/A speed=1.77x    
frame= 3850 fps= 43 q=28.0 size=N/A time=00:02:40.11 bitrate=N/A speed=1.78x    
frame= 3877 fps= 43 q=28.0 size=N/A time=00:02:41.24 bitrate=N/A speed=1.78x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c853.ts' for writing
frame= 3902 fps= 43 q=28.0 size=N/A time=00:02:42.28 bitrate=N/A speed=1.78x    
frame= 3929 fps= 43 q=28.0 size=N/A time=00:02:43.41 bitrate=N/A speed=1.78x    
frame= 3956 fps= 43 q=28.0 size=N/A time=00:02:44.53 bitrate=N/A speed=1.79x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c854.ts' for writing
frame= 3978 fps= 43 q=28.0 size=N/A time=00:02:45.45 bitrate=N/A speed=1.79x    
frame= 4003 fps= 43 q=28.0 size=N/A time=00:02:46.49 bitrate=N/A speed=1.79x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c855.ts' for writing
frame= 4029 fps= 43 q=28.0 size=N/A time=00:02:47.58 bitrate=N/A speed=1.79x    
frame= 4058 fps= 43 q=28.0 size=N/A time=00:02:48.79 bitrate=N/A speed=1.79x    
frame= 4081 fps= 43 q=28.0 size=N/A time=00:02:49.75 bitrate=N/A speed=1.79x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c856.ts' for writing
frame= 4104 fps= 43 q=28.0 size=N/A time=00:02:50.71 bitrate=N/A speed=1.79x    
frame= 4125 fps= 43 q=28.0 size=N/A time=00:02:51.58 bitrate=N/A speed=1.79x    
frame= 4151 fps= 43 q=28.0 size=N/A time=00:02:52.67 bitrate=N/A speed= 1.8x    
frame= 4171 fps= 43 q=28.0 size=N/A time=00:02:53.50 bitrate=N/A speed= 1.8x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c857.ts' for writing
frame= 4191 fps= 43 q=28.0 size=N/A time=00:02:54.34 bitrate=N/A speed= 1.8x    
frame= 4213 fps= 43 q=28.0 size=N/A time=00:02:55.25 bitrate=N/A speed= 1.8x    
frame= 4235 fps= 43 q=28.0 size=N/A time=00:02:56.17 bitrate=N/A speed= 1.8x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c858.ts' for writing
frame= 4255 fps= 43 q=28.0 size=N/A time=00:02:57.01 bitrate=N/A speed= 1.8x    
frame= 4274 fps= 43 q=28.0 size=N/A time=00:02:57.80 bitrate=N/A speed=1.79x    
frame= 4295 fps= 43 q=28.0 size=N/A time=00:02:58.67 bitrate=N/A speed=1.79x    
frame= 4316 fps= 43 q=28.0 size=N/A time=00:02:59.55 bitrate=N/A speed=1.79x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c859.ts' for writing
frame= 4342 fps= 43 q=28.0 size=N/A time=00:03:00.63 bitrate=N/A speed= 1.8x    
frame= 4365 fps= 43 q=28.0 size=N/A time=00:03:01.63 bitrate=N/A speed= 1.8x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c860.ts' for writing
frame= 4389 fps= 43 q=28.0 size=N/A time=00:03:02.59 bitrate=N/A speed= 1.8x    
frame= 4413 fps= 43 q=28.0 size=N/A time=00:03:03.60 bitrate=N/A speed= 1.8x    
frame= 4438 fps= 43 q=28.0 size=N/A time=00:03:04.64 bitrate=N/A speed= 1.8x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c861.ts' for writing
frame= 4463 fps= 43 q=28.0 size=N/A time=00:03:05.68 bitrate=N/A speed= 1.8x    
frame= 4487 fps= 43 q=28.0 size=N/A time=00:03:06.68 bitrate=N/A speed= 1.8x    
frame= 4515 fps= 43 q=28.0 size=N/A time=00:03:07.85 bitrate=N/A speed= 1.8x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c862.ts' for writing
frame= 4543 fps= 43 q=28.0 size=N/A time=00:03:09.02 bitrate=N/A speed=1.81x    
frame= 4566 fps= 43 q=28.0 size=N/A time=00:03:09.98 bitrate=N/A speed=1.81x    
frame= 4587 fps= 43 q=28.0 size=N/A time=00:03:10.85 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c863.ts' for writing
frame= 4608 fps= 43 q=28.0 size=N/A time=00:03:11.73 bitrate=N/A speed=1.81x    
frame= 4629 fps= 43 q=28.0 size=N/A time=00:03:12.60 bitrate=N/A speed=1.81x    
frame= 4651 fps= 43 q=28.0 size=N/A time=00:03:13.52 bitrate=N/A speed=1.81x    
frame= 4674 fps= 43 q=28.0 size=N/A time=00:03:14.48 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c864.ts' for writing
frame= 4694 fps= 43 q=28.0 size=N/A time=00:03:15.32 bitrate=N/A speed=1.81x    
frame= 4717 fps= 43 q=28.0 size=N/A time=00:03:16.27 bitrate=N/A speed=1.81x    
frame= 4740 fps= 43 q=28.0 size=N/A time=00:03:17.23 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c865.ts' for writing
frame= 4763 fps= 43 q=28.0 size=N/A time=00:03:18.19 bitrate=N/A speed=1.81x    
frame= 4786 fps= 43 q=28.0 size=N/A time=00:03:19.15 bitrate=N/A speed=1.81x    
frame= 4807 fps= 43 q=28.0 size=N/A time=00:03:20.03 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c866.ts' for writing
frame= 4828 fps= 43 q=28.0 size=N/A time=00:03:20.90 bitrate=N/A speed=1.81x    
frame= 4851 fps= 43 q=28.0 size=N/A time=00:03:21.86 bitrate=N/A speed=1.81x    
frame= 4872 fps= 43 q=28.0 size=N/A time=00:03:22.74 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c867.ts' for writing
frame= 4893 fps= 43 q=28.0 size=N/A time=00:03:23.74 bitrate=N/A speed=1.81x    
frame= 4919 fps= 43 q=28.0 size=N/A time=00:03:24.70 bitrate=N/A speed=1.81x    
frame= 4942 fps= 43 q=28.0 size=N/A time=00:03:25.66 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c868.ts' for writing
frame= 4968 fps= 44 q=28.0 size=N/A time=00:03:26.74 bitrate=N/A speed=1.81x    
frame= 4995 fps= 44 q=28.0 size=N/A time=00:03:27.91 bitrate=N/A speed=1.81x    
frame= 5017 fps= 44 q=28.0 size=N/A time=00:03:28.79 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c869.ts' for writing
frame= 5039 fps= 44 q=28.0 size=N/A time=00:03:29.70 bitrate=N/A speed=1.81x    
frame= 5063 fps= 44 q=28.0 size=N/A time=00:03:30.71 bitrate=N/A speed=1.81x    
frame= 5086 fps= 44 q=28.0 size=N/A time=00:03:31.66 bitrate=N/A speed=1.81x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c870.ts' for writing
frame= 5111 fps= 44 q=28.0 size=N/A time=00:03:32.71 bitrate=N/A speed=1.82x    
frame= 5132 fps= 44 q=28.0 size=N/A time=00:03:33.58 bitrate=N/A speed=1.82x    
frame= 5157 fps= 44 q=28.0 size=N/A time=00:03:34.63 bitrate=N/A speed=1.82x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c871.ts' for writing
frame= 5180 fps= 44 q=28.0 size=N/A time=00:03:35.59 bitrate=N/A speed=1.82x    
frame= 5203 fps= 44 q=28.0 size=N/A time=00:03:36.54 bitrate=N/A speed=1.82x    
frame= 5225 fps= 44 q=28.0 size=N/A time=00:03:37.46 bitrate=N/A speed=1.82x    
frame= 5247 fps= 44 q=28.0 size=N/A time=00:03:38.38 bitrate=N/A speed=1.82x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c872.ts' for writing
frame= 5267 fps= 44 q=28.0 size=N/A time=00:03:39.21 bitrate=N/A speed=1.82x    
frame= 5287 fps= 44 q=28.0 size=N/A time=00:03:40.05 bitrate=N/A speed=1.82x    
frame= 5311 fps= 44 q=28.0 size=N/A time=00:03:41.05 bitrate=N/A speed=1.82x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c873.ts' for writing
frame= 5330 fps= 44 q=28.0 size=N/A time=00:03:41.84 bitrate=N/A speed=1.82x    
frame= 5352 fps= 44 q=28.0 size=N/A time=00:03:42.76 bitrate=N/A speed=1.82x    
frame= 5371 fps= 44 q=28.0 size=N/A time=00:03:43.55 bitrate=N/A speed=1.82x    
frame= 5392 fps= 44 q=28.0 size=N/A time=00:03:44.43 bitrate=N/A speed=1.82x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c874.ts' for writing
frame= 5415 fps= 44 q=28.0 size=N/A time=00:03:45.39 bitrate=N/A speed=1.82x    
frame= 5435 fps= 44 q=28.0 size=N/A time=00:03:46.22 bitrate=N/A speed=1.81x    
frame= 5457 fps= 44 q=28.0 size=N/A time=00:03:47.14 bitrate=N/A speed=1.82x    
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c875.ts' for writing
frame= 5477 fps= 44 q=28.0 size=N/A time=00:03:47.97 bitrate=N/A speed=1.81x    
frame= 5500 fps= 44 q=28.0 size=N/A time=00:03:48.93 bitrate=N/A speed=1.81x    

[q] command received. Exiting.

[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c876.ts' for writing
[hls @ 0x56074db1ea80] Opening '/cache/transcodes/762c645940747f901ec70a0e5555697c877.ts' for writing
[out#0/hls @ 0x56074db1e300] video:140139KiB audio:18199KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame= 5583 fps= 44 q=-1.0 Lsize=N/A time=00:03:52.39 bitrate=N/A dup=1 drop=0 speed=1.83x    
[libx264 @ 0x56074db21b80] frame I:78    Avg QP:15.79  size:146867
[libx264 @ 0x56074db21b80] frame P:1711  Avg QP:20.11  size: 47886
[libx264 @ 0x56074db21b80] frame B:3794  Avg QP:23.22  size: 13208
[libx264 @ 0x56074db21b80] consecutive B-frames:  7.1%  4.6%  7.3% 81.1%
[libx264 @ 0x56074db21b80] mb I  I16..4: 34.2% 39.1% 26.6%
[libx264 @ 0x56074db21b80] mb P  I16..4:  6.9% 18.7%  3.1%  P16..4: 13.6%  6.1%  2.7%  0.0%  0.0%    skip:48.9%
[libx264 @ 0x56074db21b80] mb B  I16..4:  1.1%  3.2%  0.2%  B16..8: 10.6%  3.5%  0.3%  direct: 3.3%  skip:77.8%  L0:41.5% L1:46.1% BI:12.4%
[libx264 @ 0x56074db21b80] 8x8 transform intra:63.8% inter:44.3%
[libx264 @ 0x56074db21b80] coded y,uvDC,uvAC intra: 45.7% 38.4% 12.0% inter: 4.7% 4.2% 0.3%
[libx264 @ 0x56074db21b80] i16 v,h,dc,p: 48% 27%  9% 16%
[libx264 @ 0x56074db21b80] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 14% 19% 15%  9%  9%  8% 10%  7% 10%
[libx264 @ 0x56074db21b80] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 19% 21% 10% 10%  8%  8%  8%  7%  9%
[libx264 @ 0x56074db21b80] i8c dc,h,v,p: 49% 24% 19%  9%
[libx264 @ 0x56074db21b80] Weighted P-Frames: Y:0.9% UV:0.6%
[libx264 @ 0x56074db21b80] kb/s:4930.11