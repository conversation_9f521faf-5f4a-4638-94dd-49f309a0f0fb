{"level":"info","ts":1750849762.122617,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55540","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeradarr.duckdns.org","uri":"/.well-known/acme-challenge/X3RCTIkdpEKZzbk-9-l7Hy-LJIjXuDFZhGwJn080qFM","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000039209,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849762.8589816,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55568","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeradarr.duckdns.org","uri":"/.well-known/acme-challenge/X3RCTIkdpEKZzbk-9-l7Hy-LJIjXuDFZhGwJn080qFM","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000047722,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849763.4645064,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55572","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeradarr.duckdns.org","uri":"/.well-known/acme-challenge/X3RCTIkdpEKZzbk-9-l7Hy-LJIjXuDFZhGwJn080qFM","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000044296,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849763.608169,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55580","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeradarr.duckdns.org","uri":"/.well-known/acme-challenge/X3RCTIkdpEKZzbk-9-l7Hy-LJIjXuDFZhGwJn080qFM","headers":{"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"]}},"bytes_read":0,"user_id":"","duration":0.000045771,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849774.6449423,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48570","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeradarr.duckdns.org","uri":"/.well-known/acme-challenge/X3RCTIkdpEKZzbk-9-l7Hy-LJIjXuDFZhGwJn080qFM","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000045662,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"error","ts":1750849794.434387,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55336","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeradarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000913272,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849794.6847022,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55344","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeradarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00068886,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849810.2349834,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"42672","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeradarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000813448,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849821.198872,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"36050","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeradarr.duckdns.org","uri":"/","headers":{"Sec-Fetch-User":["?1"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Purpose":["prefetch;prerender"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Sec-Ch-Ua-Platform":["\"Windows\""],"Cookie":["REDACTED"],"Sec-Fetch-Site":["none"],"Purpose":["prefetch"],"Sec-Fetch-Mode":["navigate"],"Sec-Fetch-Dest":["document"],"Sec-Ch-Ua-Mobile":["?0"],"Upgrade-Insecure-Requests":["1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Priority":["u=0, i"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001292646,"size":34,"status":503,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"]}}
{"level":"error","ts":1750849821.210323,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"36050","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeradarr.duckdns.org","uri":"/","headers":{"Sec-Fetch-Dest":["document"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Cookie":["REDACTED"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"],"Sec-Fetch-Site":["none"],"Sec-Fetch-User":["?1"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Priority":["u=0, i"],"Sec-Ch-Ua-Mobile":["?0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000951892,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849855.5874653,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60798","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeradarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001331156,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849892.880905,"logger":"http.log.access.log5","msg":"handled request","request":{"remote_ip":"**********","remote_port":"52068","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeradarr.duckdns.org","uri":"/api/v3/history?page=1&pageSize=100&sortKey=date&sortDirection=descending&eventType=3&includeMovie=true","headers":{"Connection":["Keep-Alive"],"Authorization":["REDACTED"],"Accept-Encoding":["gzip"],"X-Api-Key":["cfc0cde90b0f483eb4190dc634ca86f2"],"Content-Length":["0"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankeradarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000642603,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
