{"Protocol":0,"Id":"a2b33c0760814d1fcaa6dd54c3de2197","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E06 - Rest and Ricklaxation HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":251168446,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S03E06 - Rest and Ricklaxation HDTV-720p","IsRemote":false,"ETag":"ab7b131bcdecf8eafb3a532b50f553aa","RunTimeTicks":13031040000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1126437,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":107,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":130,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1541970,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E06 - Rest and Ricklaxation HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/5edbce0918324626e228678796ac5a31%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/5edbce0918324626e228678796ac5a31.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x55ae2adb9340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E06 - Rest and Ricklaxation HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Descanso y Ricklajación
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Rick y Morty necesitan un descanso. Son tiempos díficiles.
    show            : Rick y Morty
    episode_id      : 6
    season_number   : 3
  Duration: 00:21:43.10, start: 0.000000, bitrate: 1541 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1126 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x55ae2ae15400] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/5edbce0918324626e228678796ac5a31.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1126 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a310.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a311.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a312.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a313.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a314.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a315.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a316.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a317.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a318.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a319.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3110.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3111.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3112.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3113.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3114.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3115.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3116.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3117.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3118.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3119.ts' for writing
size=N/A time=00:00:55.07 bitrate=N/A speed=55.1x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3120.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3121.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3122.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3123.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3124.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3125.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3126.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3127.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3128.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3129.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3130.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3131.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3132.ts' for writing
size=N/A time=00:02:12.12 bitrate=N/A speed=88.1x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3133.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3134.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3135.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3136.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3137.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3138.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3139.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3140.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3141.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3142.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3143.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3144.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3145.ts' for writing
size=N/A time=00:03:28.54 bitrate=N/A speed= 104x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3146.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3147.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3148.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3149.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3150.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3151.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3152.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3153.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3154.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3155.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3156.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3157.ts' for writing
size=N/A time=00:04:45.40 bitrate=N/A speed= 114x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3158.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3159.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3160.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3161.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3162.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3163.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3164.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3165.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3166.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3167.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3168.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3169.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3170.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3171.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3172.ts' for writing
size=N/A time=00:06:14.27 bitrate=N/A speed= 125x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3173.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3174.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3175.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3176.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3177.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3178.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3179.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3180.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3181.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3182.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3183.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3184.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3185.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3186.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3187.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3188.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3189.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3190.ts' for writing
size=N/A time=00:08:03.26 bitrate=N/A speed= 138x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3191.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3192.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3193.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3194.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3195.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3196.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3197.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3198.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a3199.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31100.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31101.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31102.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31103.ts' for writing
size=N/A time=00:09:22.24 bitrate=N/A speed= 141x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31104.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31105.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31106.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31107.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31108.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31109.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31110.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31111.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31112.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31113.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31114.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31115.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31116.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31117.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31118.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31119.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31120.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31121.ts' for writing
size=N/A time=00:11:04.12 bitrate=N/A speed= 148x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31122.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31123.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31124.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31125.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31126.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31127.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31128.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31129.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31130.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31131.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31132.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31133.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31134.ts' for writing
size=N/A time=00:12:22.20 bitrate=N/A speed= 148x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31135.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31136.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31137.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31138.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31139.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31140.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31141.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31142.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31143.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31144.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31145.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31146.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31147.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31148.ts' for writing
size=N/A time=00:13:46.46 bitrate=N/A speed= 150x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31149.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31150.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31151.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31152.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31153.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31154.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31155.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31156.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31157.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31158.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31159.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31160.ts' for writing
size=N/A time=00:14:58.20 bitrate=N/A speed= 150x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31161.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31162.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31163.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31164.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31165.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31166.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31167.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31168.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31169.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31170.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31171.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31172.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31173.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31174.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31175.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31176.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31177.ts' for writing
size=N/A time=00:16:42.20 bitrate=N/A speed= 154x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31178.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31179.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31180.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31181.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31182.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31183.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31184.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31185.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31186.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31187.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31188.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31189.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31190.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31191.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31192.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31193.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31194.ts' for writing
size=N/A time=00:18:28.60 bitrate=N/A speed= 158x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31195.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31196.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31197.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31198.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31199.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31200.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31201.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31202.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31203.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31204.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31205.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31206.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31207.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31208.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31209.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31210.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31211.ts' for writing
size=N/A time=00:20:08.89 bitrate=N/A speed= 161x    
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31212.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31213.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31214.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31215.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31216.ts' for writing
[hls @ 0x55ae2ae137c0] Opening '/cache/transcodes/5edbce0918324626e228678796ac5a31217.ts' for writing
[out#0/hls @ 0x55ae2ae15400] video:179164KiB audio:20361KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:20:35.03 bitrate=N/A speed= 162x    