{"Protocol":0,"Id":"0b6fd06e089bb8c1f230df35550c9ac8","Path":"/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":38242705853,"Name":"How to Train Your Dragon (2010) Remux-2160p","IsRemote":false,"ETag":"c48facbf023e8a2b5e49a9f09647c736","RunTimeTicks":58907520000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"hevc","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt2020nc","ColorTransfer":"smpte2084","ColorPrimaries":"bt2020","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":2,"VideoRangeType":2,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"4K HEVC HDR","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":51935923,"BitDepth":10,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main 10","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p10le","Level":153,"IsAnamorphic":false},{"Codec":"dts","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - DTS - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":768000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"ac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"dts","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":2,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - DTS-HD MA \u002B DTS:X - 7.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"7.1","BitRate":5553891,"BitDepth":24,"RefFrames":null,"PacketLength":null,"Channels":8,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS-HD MA \u002B DTS:X","Type":0,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Forzados","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Forzados - Spanish - Predeterminado - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"PGSSUB","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - PGSSUB","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":6,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":58897814,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G  -i file:"/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 9232000 -bufsize 18464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 "expr:gte(t,n_forced*3)" -sc_threshold:v:0 0 -vf "setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,1920)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d57%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d57.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, matroska,webm, from 'file:/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv':
  Metadata:
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2019-01-10T20:42:53.000000Z
  Duration: 01:38:10.75, start: 0.000000, bitrate: 51935 kb/s
  Chapters:
    Chapter #0:0: start 0.000000, end 543.793244
      Metadata:
        title           : Chapter 01
    Chapter #0:1: start 543.793244, end 716.006956
      Metadata:
        title           : Chapter 02
    Chapter #0:2: start 716.006956, end 935.059111
      Metadata:
        title           : Chapter 03
    Chapter #0:3: start 935.059111, end 1341.089733
      Metadata:
        title           : Chapter 04
    Chapter #0:4: start 1341.089733, end 1685.725689
      Metadata:
        title           : Chapter 05
    Chapter #0:5: start 1685.725689, end 2018.224533
      Metadata:
        title           : Chapter 06
    Chapter #0:6: start 2018.224533, end 2371.160444
      Metadata:
        title           : Chapter 07
    Chapter #0:7: start 2371.160444, end 2632.713400
      Metadata:
        title           : Chapter 08
    Chapter #0:8: start 2632.713400, end 2852.391200
      Metadata:
        title           : Chapter 09
    Chapter #0:9: start 2852.391200, end 3011.216533
      Metadata:
        title           : Chapter 10
    Chapter #0:10: start 3011.216533, end 3619.824533
      Metadata:
        title           : Chapter 11
    Chapter #0:11: start 3619.824533, end 3911.407489
      Metadata:
        title           : Chapter 12
    Chapter #0:12: start 3911.407489, end 4192.188000
      Metadata:
        title           : Chapter 13
    Chapter #0:13: start 4192.188000, end 4382.211156
      Metadata:
        title           : Chapter 14
    Chapter #0:14: start 4382.211156, end 5030.066689
      Metadata:
        title           : Chapter 15
    Chapter #0:15: start 5030.066689, end 5358.519822
      Metadata:
        title           : Chapter 16
    Chapter #0:16: start 5358.519822, end 5890.752000
      Metadata:
        title           : Chapter 17
  Stream #0:0: Video: hevc (Main 10), yuv420p10le(tv, bt2020nc/bt2020/smpte2084), 3840x2160 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 44932856
        DURATION-eng    : 01:38:10.718000000
        NUMBER_OF_FRAMES-eng: 141236
        NUMBER_OF_BYTES-eng: 33085848639
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: dts (dca) (DTS), 48000 Hz, 5.1(side), fltp, 768 kb/s (default) (forced)
      Metadata:
        BPS-eng         : 768000
        DURATION-eng    : 01:38:10.720000000
        NUMBER_OF_FRAMES-eng: 552255
        NUMBER_OF_BYTES-eng: 565509120
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(spa): Audio: ac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        BPS-eng         : 640000
        DURATION-eng    : 01:38:10.752000000
        NUMBER_OF_FRAMES-eng: 184086
        NUMBER_OF_BYTES-eng: 471260160
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(eng): Audio: dts (dca) (DTS-HD MA + DTS:X), 48000 Hz, 7.1, s32p (24 bit)
      Metadata:
        BPS-eng         : 5553891
        DURATION-eng    : 01:38:10.720000000
        NUMBER_OF_FRAMES-eng: 552255
        NUMBER_OF_BYTES-eng: 4089552576
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(spa): Subtitle: subrip (srt) (default) (forced)
      Metadata:
        title           : Forzados
        BPS-eng         : 0
        DURATION-eng    : 00:08:47.818000000
        NUMBER_OF_FRAMES-eng: 2
        NUMBER_OF_BYTES-eng: 44
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:5(spa): Subtitle: subrip (srt)
      Metadata:
        BPS-eng         : 42
        DURATION-eng    : 01:37:08.113000000
        NUMBER_OF_FRAMES-eng: 870
        NUMBER_OF_BYTES-eng: 30771
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:6(eng): Subtitle: hdmv_pgs_subtitle (pgssub), 1920x1080
      Metadata:
        BPS-eng         : 39065
        DURATION-eng    : 01:30:28.715000000
        NUMBER_OF_FRAMES-eng: 2395
        NUMBER_OF_BYTES-eng: 26509155
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 20:42:53
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
[tonemapx @ 0x56407133f0c0] Using CPU capabilities: AVX2 FMA3
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (dts (dca) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[tonemapx @ 0x7f07c0012140] Using CPU capabilities: AVX2 FMA3
[libx264 @ 0x5640712e27c0] using SAR=1/1
[libx264 @ 0x5640712e27c0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5640712e27c0] profile High, level 4.0, 4:2:0, 8-bit
[libx264 @ 0x5640712e27c0] 264 - core 164 r3095 baee400 - H.264/MPEG-4 AVC codec - Copyleft 2003-2022 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=0 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1 trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=18 lookahead_threads=3 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=23 scenecut=0 intra_refresh=0 rc_lookahead=10 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 vbv_maxrate=9232 vbv_bufsize=18464 crf_max=0.0 nal_hrd=none filler=0 ip_ratio=1.40 aq=1:1.00
Output #0, hls, to '/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d57.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264, yuv420p(tv, bt709, progressive), 1920x1080 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 90k tbn (default)
      Metadata:
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 9232000/0/0 buffer size: 18464000 vbv_delay: N/A
  Stream #0:1: Audio: aac, 48000 Hz, 5.1, s16, 640 kb/s (default) (forced)
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=   10 fps= 10 q=28.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   38 fps= 25 q=28.0 size=N/A time=00:00:01.16 bitrate=N/A speed=0.778x    
frame=   68 fps= 34 q=28.0 size=N/A time=00:00:02.41 bitrate=N/A speed=1.21x    
[hls @ 0x56407130f2c0] Opening '/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d570.ts' for writing
frame=   98 fps= 39 q=28.0 size=N/A time=00:00:03.67 bitrate=N/A speed=1.47x    
frame=  126 fps= 42 q=28.0 size=N/A time=00:00:04.83 bitrate=N/A speed=1.61x    
[hls @ 0x56407130f2c0] Opening '/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d571.ts' for writing
frame=  157 fps= 45 q=28.0 size=N/A time=00:00:06.13 bitrate=N/A speed=1.75x    
frame=  185 fps= 46 q=28.0 size=N/A time=00:00:07.29 bitrate=N/A speed=1.82x    
frame=  213 fps= 47 q=28.0 size=N/A time=00:00:08.46 bitrate=N/A speed=1.88x    
[hls @ 0x56407130f2c0] Opening '/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d572.ts' for writing


[q] command received. Exiting.

[hls @ 0x56407130f2c0] Opening '/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d573.ts' for writing
[hls @ 0x56407130f2c0] Opening '/cache/transcodes/ec7b659dcc1c30e2cca957cc6d207d574.ts' for writing
[out#0/hls @ 0x56407672be80] video:4712KiB audio:969KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=  297 fps= 54 q=-1.0 Lsize=N/A time=00:00:11.97 bitrate=N/A dup=2 drop=0 speed=2.16x    
[libx264 @ 0x5640712e27c0] frame I:5     Avg QP:15.50  size: 66860
[libx264 @ 0x5640712e27c0] frame P:83    Avg QP:20.25  size: 33210
[libx264 @ 0x5640712e27c0] frame B:209   Avg QP:22.87  size:  8297
[libx264 @ 0x5640712e27c0] consecutive B-frames:  4.4%  4.7%  2.0% 88.9%
[libx264 @ 0x5640712e27c0] mb I  I16..4: 54.6% 32.5% 12.9%
[libx264 @ 0x5640712e27c0] mb P  I16..4: 14.4% 24.7%  3.7%  P16..4: 11.8%  4.8%  1.1%  0.0%  0.0%    skip:39.5%
[libx264 @ 0x5640712e27c0] mb B  I16..4:  2.1%  2.4%  0.1%  B16..8: 16.3%  3.9%  0.1%  direct: 6.1%  skip:69.0%  L0:41.3% L1:44.1% BI:14.5%
[libx264 @ 0x5640712e27c0] 8x8 transform intra:54.3% inter:40.0%
[libx264 @ 0x5640712e27c0] coded y,uvDC,uvAC intra: 45.9% 53.5% 22.9% inter: 3.3% 11.7% 0.4%
[libx264 @ 0x5640712e27c0] i16 v,h,dc,p: 37% 29% 16% 18%
[libx264 @ 0x5640712e27c0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu:  8% 25% 20%  7%  7%  4% 14%  3% 10%
[libx264 @ 0x5640712e27c0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu:  8% 38%  9%  6%  7%  4% 14%  2% 10%
[libx264 @ 0x5640712e27c0] i8c dc,h,v,p: 43% 32% 14% 10%
[libx264 @ 0x5640712e27c0] Weighted P-Frames: Y:2.4% UV:0.0%
[libx264 @ 0x5640712e27c0] kb/s:3115.94