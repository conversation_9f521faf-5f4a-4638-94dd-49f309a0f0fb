{"level":"info","ts":1750849758.4818683,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55446","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/.well-known/acme-challenge/whTCmlISiPVUGHfG8m5kK-GdtIm7kU6O53N3E9jyBew","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.0000501,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849759.0711806,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55458","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/.well-known/acme-challenge/whTCmlISiPVUGHfG8m5kK-GdtIm7kU6O53N3E9jyBew","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000047573,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849759.3266094,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55468","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/.well-known/acme-challenge/whTCmlISiPVUGHfG8m5kK-GdtIm7kU6O53N3E9jyBew","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000030637,"size":87,"status":200,"resp_headers":{"Content-Type":["text/plain"],"Server":["Caddy"]}}
{"level":"info","ts":1750849759.4146657,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55472","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/.well-known/acme-challenge/whTCmlISiPVUGHfG8m5kK-GdtIm7kU6O53N3E9jyBew","headers":{"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"]}},"bytes_read":0,"user_id":"","duration":0.000048301,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849764.3743198,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55598","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/.well-known/acme-challenge/whTCmlISiPVUGHfG8m5kK-GdtIm7kU6O53N3E9jyBew","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000045332,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"error","ts":1750849789.667634,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55310","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000642747,"size":34,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"error","ts":1750849796.695139,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49226","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000734295,"size":34,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"error","ts":1750849798.9893758,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49256","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000673394,"size":34,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"error","ts":1750849826.458778,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60092","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"Purpose":["prefetch"],"Sec-Fetch-Mode":["navigate"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Priority":["u=0, i"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Sec-Fetch-Site":["none"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Sec-Fetch-User":["?1"],"Sec-Fetch-Dest":["document"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Cookie":["REDACTED"],"Upgrade-Insecure-Requests":["1"],"Sec-Purpose":["prefetch;prerender"],"Sec-Ch-Ua-Mobile":["?0"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000726944,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849826.4756656,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60092","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"Sec-Fetch-Site":["none"],"Sec-Fetch-Dest":["document"],"Sec-Ch-Ua-Mobile":["?0"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Fetch-Mode":["navigate"],"Priority":["u=0, i"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Sec-Fetch-User":["?1"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Cookie":["REDACTED"],"Sec-Ch-Ua":["\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\""],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Ch-Ua-Platform":["\"Windows\""],"Upgrade-Insecure-Requests":["1"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00081199,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849840.0837686,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"34728","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000828044,"size":34,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"error","ts":1750849848.8687227,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60794","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000719344,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849891.3405573,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"52052","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/api/v3/history?page=1&pageSize=100&sortKey=date&sortDirection=descending&eventType=3&includeSeries=true","headers":{"Authorization":["REDACTED"],"Accept-Encoding":["gzip"],"X-Api-Key":["2f9da07e98744f4890c0960d15ead111"],"Content-Length":["0"],"Connection":["Keep-Alive"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000764527,"size":34,"status":503,"resp_headers":{"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"]}}
{"level":"error","ts":1750849913.6599932,"logger":"http.log.access.log2","msg":"handled request","request":{"remote_ip":"**********","remote_port":"43282","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankesonarr.duckdns.org","uri":"/","headers":{"Accept-Language":["en-US,en;q=0.9"],"Sec-Fetch-Mode":["navigate"],"Accept":["text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7"],"Connection":["keep-alive"],"User-Agent":["Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Sec-Ch-Ua":["Google Chrome\";v=\"111\", \"Not(A:Brand\";v=\"8\", \"Chromium\";v=\"111"],"Accept-Encoding":["gzip, deflate, br"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"http/1.1","server_name":"tankesonarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000558025,"size":34,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
