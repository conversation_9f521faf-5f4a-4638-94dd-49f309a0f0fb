{"Protocol":0,"Id":"04ee9aad9beabd2b880f6ac1a2c1a608","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 1/<PERSON> and <PERSON><PERSON><PERSON> - S01E01 - Pilot HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":280325719,"Name":"<PERSON> and <PERSON><PERSON>y - S01E01 - Pilot HDTV-720p","IsRemote":false,"ETag":"c7b05b28eaed9671ebf23f7ba033e9cd","RunTimeTicks":12680960000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1336546,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":104,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":139,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1768482,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E01 - Pilot HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/f1aefa999655e32aec909d5afea40c27%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/f1aefa999655e32aec909d5afea40c27.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x557b540ae340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E01 - Pilot HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Piloto
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Rick se muda con la familia de su hija y se convierte en una mala influencia para Morty, su nieto.
    show            : Rick y Morty
    episode_id      : 1
    season_number   : 1
  Duration: 00:21:08.10, start: 0.000000, bitrate: 1768 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1336 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x557b540b3300] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/f1aefa999655e32aec909d5afea40c27.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1336 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c270.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c271.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c272.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c273.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c274.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c275.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c276.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c277.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c278.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c279.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2710.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2711.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2712.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2713.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2714.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2715.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2716.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2717.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2718.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2719.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2720.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2721.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2722.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2723.ts' for writing
size=N/A time=00:01:13.12 bitrate=N/A speed=73.1x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2724.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2725.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2726.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2727.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2728.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2729.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2730.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2731.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2732.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2733.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2734.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2735.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2736.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2737.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2738.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2739.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2740.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2741.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2742.ts' for writing
size=N/A time=00:03:10.11 bitrate=N/A speed= 127x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2743.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2744.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2745.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2746.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2747.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2748.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2749.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2750.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2751.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2752.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2753.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2754.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2755.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2756.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2757.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2758.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2759.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2760.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2761.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2762.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2763.ts' for writing
size=N/A time=00:05:11.07 bitrate=N/A speed= 155x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2764.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2765.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2766.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2767.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2768.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2769.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2770.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2771.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2772.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2773.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2774.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2775.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2776.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2777.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2778.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2779.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2780.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2781.ts' for writing
size=N/A time=00:06:59.07 bitrate=N/A speed= 168x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2782.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2783.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2784.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2785.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2786.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2787.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2788.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2789.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2790.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2791.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2792.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2793.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2794.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2795.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2796.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2797.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2798.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c2799.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27100.ts' for writing
size=N/A time=00:08:54.75 bitrate=N/A speed= 178x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27101.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27102.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27103.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27104.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27105.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27106.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27107.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27108.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27109.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27110.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27111.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27112.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27113.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27114.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27115.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27116.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27117.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27118.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27119.ts' for writing
size=N/A time=00:10:50.91 bitrate=N/A speed= 186x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27120.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27121.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27122.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27123.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27124.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27125.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27126.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27127.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27128.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27129.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27130.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27131.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27132.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27133.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27134.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27135.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27136.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27137.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27138.ts' for writing
size=N/A time=00:12:42.75 bitrate=N/A speed= 191x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27139.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27140.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27141.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27142.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27143.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27144.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27145.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27146.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27147.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27148.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27149.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27150.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27151.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27152.ts' for writing
size=N/A time=00:14:10.40 bitrate=N/A speed= 189x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27153.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27154.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27155.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27156.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27157.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27158.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27159.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27160.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27161.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27162.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27163.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27164.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27165.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27166.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27167.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27168.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27169.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27170.ts' for writing
size=N/A time=00:16:00.70 bitrate=N/A speed= 192x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27171.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27172.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27173.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27174.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27175.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27176.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27177.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27178.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27179.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27180.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27181.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27182.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27183.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27184.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27185.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27186.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27187.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27188.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27189.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27190.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27191.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27192.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27193.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27194.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27195.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27196.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27197.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27198.ts' for writing
size=N/A time=00:18:46.56 bitrate=N/A speed= 205x    
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27199.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27200.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27201.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27202.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27203.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27204.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27205.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27206.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27207.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27208.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27209.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27210.ts' for writing
[hls @ 0x557b540b2380] Opening '/cache/transcodes/f1aefa999655e32aec909d5afea40c27211.ts' for writing
[out#0/hls @ 0x557b540b3300] video:206875KiB audio:19814KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:19:54.97 bitrate=N/A speed= 208x    