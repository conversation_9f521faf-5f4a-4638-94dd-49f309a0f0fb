[2025-06-24 00:22:51.554 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 00:22:51.566 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 00:22:51.586 +02:00] [INF] [44] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-24 01:59:58.889 +02:00] [INF] [14] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-06-24 01:59:58.989 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-06-24 01:59:59.532 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-24 02:00:00.000 +02:00, which is 00:00:00.4673408 from now.
[2025-06-24 02:00:00.140 +02:00] [INF] [105] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-06-24 02:00:00.226 +02:00] [INF] [105] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-06-24 02:00:01.001 +02:00] [INF] [107] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-25 02:00:00.000 +02:00, which is 23:59:58.9981196 from now.
[2025-06-24 02:59:58.644 +02:00] [INF] [139] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-06-24 02:59:59.437 +02:00] [INF] [139] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-24 03:00:00.000 +02:00, which is 00:00:00.5628293 from now.
[2025-06-24 03:00:00.022 +02:00] [INF] [139] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-06-24 03:00:01.002 +02:00] [INF] [141] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-25 03:00:00.000 +02:00, which is 23:59:58.9974828 from now.
[2025-06-24 04:44:15.606 +02:00] [INF] [46] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 04:44:15.607 +02:00] [INF] [46] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 04:44:15.610 +02:00] [INF] [46] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-24 05:07:33.220 +02:00] [INF] [122] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-24 05:07:33.225 +02:00] [INF] [122] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-06-24 05:07:33.227 +02:00] [INF] [122] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-06-24 05:07:33.232 +02:00] [INF] [122] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-06-24 05:07:34.178 +02:00] [WRN] [122] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-24 05:07:34.687 +02:00] [INF] [104] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-06-24 05:07:35.052 +02:00] [WRN] [101] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-24 05:07:35.759 +02:00] [INF] [121] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Parque Jurásico", Path: "/CONTENIDO/PELIS/Jurassic Park (1993)/Jurassic Park (1993) Remux-1080p.mkv", Id: 6b1add72-d74c-ffef-4ff2-84d5c1c3d00b
[2025-06-24 05:07:35.955 +02:00] [INF] [121] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Destino Final: Lazos de Sangre", Path: "/CONTENIDO/PELIS/Final Destination Bloodlines (2025)/Final Destination Bloodlines (2025) Bluray-1080p.mkv", Id: 083311e5-9c6b-791a-c4cc-e5ae31cc74cb
[2025-06-24 05:07:36.049 +02:00] [INF] [121] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Cómo entrenar a tu dragón 2", Path: "/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv", Id: 14857cd7-81a7-139a-b1db-7a466b13d843
[2025-06-24 05:07:37.493 +02:00] [INF] [85] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-24 05:07:37.545 +02:00] [INF] [85] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-06-24 05:07:38.114 +02:00] [INF] [121] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/Final Destination Bloodlines (2025)/Final Destination Bloodlines (2025) WEBDL-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-06-24 05:07:38.253 +02:00] [INF] [93] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-24 05:07:38.253 +02:00] [INF] [93] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-24 05:07:38.940 +02:00] [ERR] [90] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-06-24 05:07:50.643 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-24 05:07:50.646 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-06-24 05:07:50.646 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-06-24 05:07:50.646 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-06-24 05:07:50.922 +02:00] [INF] [128] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-06-24 05:07:50.922 +02:00] [INF] [128] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-06-24 05:07:50.922 +02:00] [INF] [128] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-06-24 05:07:50.924 +02:00] [INF] [128] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-06-24 05:07:50.924 +02:00] [INF] [128] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-06-24 05:07:50.924 +02:00] [INF] [128] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-06-24 05:07:59.098 +02:00] [INF] [109] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "cdc4ab8b8df7162ca868ae3a899a95e3" "Amblin Entertainment"
[2025-06-24 05:07:59.098 +02:00] [INF] [109] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Amblin Entertainment", Path: "/config/metadata/Studio/Amblin Entertainment", Id: cdc4ab8b-8df7-162c-a868-ae3a899a95e3
[2025-06-24 05:07:59.793 +02:00] [INF] [109] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 26 seconds
[2025-06-24 05:07:59.796 +02:00] [INF] [128] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-24 05:07:59.947 +02:00] [INF] [85] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-24 05:08:00.139 +02:00] [INF] [82] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-24 14:31:57.707 +02:00] [INF] [122] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 14:31:57.717 +02:00] [INF] [122] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 14:31:57.725 +02:00] [INF] [122] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-24 14:32:16.855 +02:00] [INF] [125] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 14:32:16.856 +02:00] [INF] [125] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 14:32:16.858 +02:00] [INF] [125] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-24 14:32:33.777 +02:00] [INF] [122] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 14:32:33.778 +02:00] [INF] [122] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-24 14:32:33.778 +02:00] [INF] [122] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-24 17:07:31.708 +02:00] [INF] [189] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar los subtítulos que faltan" Completed after 0 minute(s) and 0 seconds
[2025-06-24 17:07:31.736 +02:00] [INF] [193] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-06-24 17:07:31.751 +02:00] [INF] [87] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: Optimizing and vacuuming jellyfin.db...
[2025-06-24 17:07:32.746 +02:00] [INF] [186] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar registros" Completed after 0 minute(s) and 1 seconds
[2025-06-24 17:07:32.753 +02:00] [INF] [99] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar letras faltantes" Completed after 0 minute(s) and 1 seconds
[2025-06-24 17:07:32.758 +02:00] [INF] [188] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Normalización de audio" Completed after 0 minute(s) and 1 seconds
[2025-06-24 17:07:32.758 +02:00] [INF] [83] Emby.Server.Implementations.ScheduledTasks.TaskManager: "TasksRefreshChannels" Completed after 0 minute(s) and 1 seconds
[2025-06-24 17:07:33.019 +02:00] [INF] [87] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: jellyfin.db optimized successfully!
[2025-06-24 17:07:33.020 +02:00] [INF] [87] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Optimizar la base de datos" Completed after 0 minute(s) and 1 seconds
[2025-06-24 17:07:34.580 +02:00] [INF] [86] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 2 seconds
[2025-06-24 17:07:35.732 +02:00] [INF] [192] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Eliminar archivos temporales" Completed after 0 minute(s) and 4 seconds
[2025-06-24 17:07:35.998 +02:00] [INF] [188] Jellyfin.LiveTv.Guide.GuideManager: Refreshing guide with 7 days of guide data
[2025-06-24 17:07:36.009 +02:00] [INF] [188] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Refresh Guide" Completed after 0 minute(s) and 4 seconds
[2025-06-24 17:08:32.748 +02:00] [INF] [191] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-24 17:08:32.750 +02:00] [INF] [191] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-06-24 17:08:32.752 +02:00] [INF] [191] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-06-24 17:08:32.755 +02:00] [INF] [191] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-06-24 17:08:33.572 +02:00] [WRN] [191] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-24 17:08:34.502 +02:00] [WRN] [20] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-24 17:08:34.934 +02:00] [INF] [87] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 2 seconds
[2025-06-24 17:08:36.916 +02:00] [INF] [21] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-24 17:08:36.973 +02:00] [INF] [21] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-06-24 17:08:37.554 +02:00] [INF] [187] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-24 17:08:37.554 +02:00] [INF] [187] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-24 17:08:38.016 +02:00] [ERR] [97] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-06-24 17:09:02.291 +02:00] [INF] [102] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 29 seconds
[2025-06-24 17:09:02.295 +02:00] [INF] [100] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-24 17:09:02.469 +02:00] [INF] [193] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-24 17:09:02.675 +02:00] [INF] [94] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-24 18:33:42.015 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-06-24 18:33:42.071 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]"]
[2025-06-24 18:33:42.074 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-06-24 18:33:42.075 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-06-24 18:33:42.076 +02:00] [INF] [1] Main: Architecture: X64
[2025-06-24 18:33:42.076 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-06-24 18:33:42.076 +02:00] [INF] [1] Main: User Interactive: True
[2025-06-24 18:33:42.077 +02:00] [INF] [1] Main: Processor count: 12
[2025-06-24 18:33:42.077 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-06-24 18:33:42.077 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-06-24 18:33:42.077 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-06-24 18:33:42.077 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-06-24 18:33:42.078 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-06-24 18:33:42.078 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-06-24 18:33:42.078 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-06-24 18:33:42.319 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-06-24 18:33:42.473 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-06-24 18:33:42.555 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-06-24 18:33:42.571 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-06-24 18:33:42.592 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-06-24 18:33:42.691 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-24 18:33:42.691 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-06-24 18:33:42.692 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-24 18:33:42.694 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-06-24 18:33:42.694 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-06-24 18:33:42.695 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-06-24 18:33:42.695 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-06-24 18:33:53.508 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-06-24 18:33:53.512 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-06-24 18:33:53.513 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-06-24 18:33:53.513 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-06-24 18:33:53.514 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-06-24 18:33:53.535 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-06-24 18:33:53.535 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-06-24 18:33:53.640 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-06-24 18:33:53.661 +02:00] [WRN] [1] Microsoft.AspNetCore.DataProtection.KeyManagement.XmlKeyManager: No XML encryptor configured. Key {a23b65e6-30f2-4635-ad1f-75e71f154c20} may be persisted to storage in unencrypted form.
[2025-06-24 18:33:54.243 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-06-24 18:33:54.284 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-06-24 18:33:54.290 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-24 18:33:54.312 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-25 03:00:00.000 +02:00, which is 08:26:05.6879880 from now.
[2025-06-24 18:33:54.336 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-25 02:00:00.000 +02:00, which is 07:26:05.6632291 from now.
[2025-06-24 18:33:54.425 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-24 18:33:54.430 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-06-24 18:33:54.497 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-06-24 18:33:54.521 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-06-24 18:33:54.553 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-06-24 18:33:54.719 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-06-24 18:33:55.576 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-24 18:33:57.358 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-06-24 18:33:57.388 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-06-24 18:33:58.972 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-06-24 18:34:07.705 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-06-24 18:34:07.708 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-06-24 18:34:07.708 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-06-24 18:34:07.708 +02:00] [INF] [1] Main: Startup complete 0:00:26.0572704
