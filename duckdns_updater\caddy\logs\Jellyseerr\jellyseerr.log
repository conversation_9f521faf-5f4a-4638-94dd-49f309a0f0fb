{"level":"info","ts":1750849762.1647294,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55544","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/.well-known/acme-challenge/mDnHgUucc9b-NEJgubP4S6xReH6_erAPYRBBrIxX0Ow","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000040488,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849762.5335279,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55560","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/.well-known/acme-challenge/mDnHgUucc9b-NEJgubP4S6xReH6_erAPYRBBrIxX0Ow","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.000042308,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849763.4075096,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55570","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/.well-known/acme-challenge/mDnHgUucc9b-NEJgubP4S6xReH6_erAPYRBBrIxX0Ow","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000041449,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849763.9567754,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55582","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/.well-known/acme-challenge/mDnHgUucc9b-NEJgubP4S6xReH6_erAPYRBBrIxX0Ow","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000064102,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849775.8704119,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48582","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/.well-known/acme-challenge/mDnHgUucc9b-NEJgubP4S6xReH6_erAPYRBBrIxX0Ow","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000051446,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"error","ts":1750849863.1457915,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60106","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankejellyseerr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000924817,"size":38,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849867.9745867,"logger":"http.log.access.log6","msg":"handled request","request":{"remote_ip":"**********","remote_port":"46530","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankejellyseerr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankejellyseerr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00072691,"size":38,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
