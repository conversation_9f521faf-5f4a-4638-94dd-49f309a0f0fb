{"Protocol":0,"Id":"52300e5a972fd5d7912aa701c890b0ad","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 3/<PERSON> and <PERSON><PERSON><PERSON> - S03E09 - The ABC\u0027s of Beth HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":260820702,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S03E09 - The ABC\u0027s of Beth HDTV-720p","IsRemote":false,"ETag":"f9dcecdfb3c3b91d07e7ca17ee6dc7c8","RunTimeTicks":12820800000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1176275,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":110,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":132,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1627484,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E09 - The ABC's of Beth HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x55ca4bbd4340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E09 - The ABC's of Beth HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : El ABC de Beth
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Jerry visita a una mujer en este episodio, bro. Y Beth vuelve a su infancia.
    show            : Rick y Morty
    episode_id      : 9
    season_number   : 3
  Duration: 00:21:22.08, start: 0.000000, bitrate: 1627 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1176 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x55ca4bc43540] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1176 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6990.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6991.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6992.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6993.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6994.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6995.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6996.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6997.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6998.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d6999.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69910.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69911.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69912.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69913.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69914.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69915.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69916.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69917.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69918.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69919.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69920.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69921.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69922.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69923.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69924.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69925.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69926.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69927.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69928.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69929.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69930.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69931.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69932.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69933.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69934.ts' for writing
size=N/A time=00:01:44.00 bitrate=N/A speed= 104x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69935.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69936.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69937.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69938.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69939.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69940.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69941.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69942.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69943.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69944.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69945.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69946.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69947.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69948.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69949.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69950.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69951.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69952.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69953.ts' for writing
size=N/A time=00:03:36.60 bitrate=N/A speed= 144x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69954.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69955.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69956.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69957.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69958.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69959.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69960.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69961.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69962.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69963.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69964.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69965.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69966.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69967.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69968.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69969.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69970.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69971.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69972.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69973.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69974.ts' for writing
size=N/A time=00:05:44.67 bitrate=N/A speed= 172x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69975.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69976.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69977.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69978.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69979.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69980.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69981.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69982.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69983.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69984.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69985.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69986.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69987.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69988.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69989.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69990.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69991.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69992.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69993.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69994.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69995.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69996.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69997.ts' for writing
size=N/A time=00:08:03.80 bitrate=N/A speed= 193x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69998.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d69999.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699100.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699101.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699102.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699103.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699104.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699105.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699106.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699107.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699108.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699109.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699110.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699111.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699112.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699113.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699114.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699115.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699116.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699117.ts' for writing
size=N/A time=00:10:04.48 bitrate=N/A speed= 201x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699118.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699119.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699120.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699121.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699122.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699123.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699124.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699125.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699126.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699127.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699128.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699129.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699130.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699131.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699132.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699133.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699134.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699135.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699136.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699137.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699138.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699139.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699140.ts' for writing
size=N/A time=00:12:21.92 bitrate=N/A speed= 212x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699141.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699142.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699143.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699144.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699145.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699146.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699147.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699148.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699149.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699150.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699151.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699152.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699153.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699154.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699155.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699156.ts' for writing
size=N/A time=00:13:58.43 bitrate=N/A speed= 209x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699157.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699158.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699159.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699160.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699161.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699162.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699163.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699164.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699165.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699166.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699167.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699168.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699169.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699170.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699171.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699172.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699173.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699174.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699175.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699176.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699177.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699178.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699179.ts' for writing
size=N/A time=00:16:15.96 bitrate=N/A speed= 217x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699180.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699181.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699182.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699183.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699184.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699185.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699186.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699187.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699188.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699189.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699190.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699191.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699192.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699193.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699194.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699195.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699196.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699197.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699198.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699199.ts' for writing
size=N/A time=00:18:19.77 bitrate=N/A speed= 220x    
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699200.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699201.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699202.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699203.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699204.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699205.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699206.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699207.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699208.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699209.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699210.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699211.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699212.ts' for writing
[hls @ 0x55ca4bc2d700] Opening '/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699213.ts' for writing
[out#0/hls @ 0x55ca4bc43540] video:184076KiB audio:20032KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:19:33.90 bitrate=N/A speed= 227x    