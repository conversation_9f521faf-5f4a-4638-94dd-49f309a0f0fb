{"Protocol":0,"Id":"6dce3e472d650706e596024d4f113e42","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 2/<PERSON> and <PERSON><PERSON><PERSON> - S02E06 - The Ricks Must Be Crazy HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":305042606,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S02E06 - The Ricks Must Be Crazy HDTV-720p","IsRemote":false,"ETag":"75ceece737dfd6a273ee75e9ee850c02","RunTimeTicks":12870080000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1468708,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":118,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":95,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1896134,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E06 - The Ricks Must Be Crazy HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x559364f99340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E06 - The Ricks Must Be Crazy HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Los Ricks deben estar locos
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Rick olvidó revisar el aceite de su coche y tiene que apañarselas para arreglarlo.
    show            : Rick y Morty
    episode_id      : 6
    season_number   : 2
  Duration: 00:21:27.01, start: 0.000000, bitrate: 1896 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1468 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x559364ff0f40] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1468 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a30.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a31.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a32.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a33.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a34.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a35.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a36.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a37.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a38.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a39.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a310.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a311.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a312.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a313.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a314.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a315.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a316.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a317.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a318.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a319.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a320.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a321.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a322.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a323.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a324.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a325.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a326.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a327.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a328.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a329.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a330.ts' for writing
size=N/A time=00:01:33.28 bitrate=N/A speed=93.3x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a331.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a332.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a333.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a334.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a335.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a336.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a337.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a338.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a339.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a340.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a341.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a342.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a343.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a344.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a345.ts' for writing
size=N/A time=00:03:00.89 bitrate=N/A speed= 121x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a346.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a347.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a348.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a349.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a350.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a351.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a352.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a353.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a354.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a355.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a356.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a357.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a358.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a359.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a360.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a361.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a362.ts' for writing
size=N/A time=00:04:44.00 bitrate=N/A speed= 142x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a363.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a364.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a365.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a366.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a367.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a368.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a369.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a370.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a371.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a372.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a373.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a374.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a375.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a376.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a377.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a378.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a379.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a380.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a381.ts' for writing
size=N/A time=00:06:39.80 bitrate=N/A speed= 160x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a382.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a383.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a384.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a385.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a386.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a387.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a388.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a389.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a390.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a391.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a392.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a393.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a394.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a395.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a396.ts' for writing
size=N/A time=00:08:11.80 bitrate=N/A speed= 164x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a397.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a398.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a399.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3100.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3101.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3102.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3103.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3104.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3105.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3106.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3107.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3108.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3109.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3110.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3111.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3112.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3113.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3114.ts' for writing
size=N/A time=00:09:56.00 bitrate=N/A speed= 170x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3115.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3116.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3117.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3118.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3119.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3120.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3121.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3122.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3123.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3124.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3125.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3126.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3127.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3128.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3129.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3130.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3131.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3132.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3133.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3134.ts' for writing
size=N/A time=00:12:01.79 bitrate=N/A speed= 180x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3135.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3136.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3137.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3138.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3139.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3140.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3141.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3142.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3143.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3144.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3145.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3146.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3147.ts' for writing
size=N/A time=00:13:19.29 bitrate=N/A speed= 178x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3148.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3149.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3150.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3151.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3152.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3153.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3154.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3155.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3156.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3157.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3158.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3159.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3160.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3161.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3162.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3163.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3164.ts' for writing
size=N/A time=00:14:55.77 bitrate=N/A speed= 179x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3165.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3166.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3167.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3168.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3169.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3170.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3171.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3172.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3173.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3174.ts' for writing
size=N/A time=00:15:56.67 bitrate=N/A speed= 174x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3175.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3176.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3177.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3178.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3179.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3180.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3181.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3182.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3183.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3184.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3185.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3186.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3187.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3188.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3189.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3190.ts' for writing
size=N/A time=00:17:31.52 bitrate=N/A speed= 175x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3191.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3192.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3193.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3194.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3195.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3196.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3197.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3198.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3199.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3200.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3201.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3202.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3203.ts' for writing
size=N/A time=00:18:53.44 bitrate=N/A speed= 174x    
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3204.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3205.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3206.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3207.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3208.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3209.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3210.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3211.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3212.ts' for writing
[hls @ 0x559364feabc0] Opening '/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3213.ts' for writing
[out#0/hls @ 0x559364ff0f40] video:230725KiB audio:20110KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:19:50.82 bitrate=N/A speed= 177x    