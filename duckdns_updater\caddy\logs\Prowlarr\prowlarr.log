{"level":"info","ts":1750849761.0686946,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55522","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/.well-known/acme-challenge/BVg3p2QFuPxwLUVr8i45ofg1ysvW2Ol4qMzpTeNd-_8","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000046501,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849761.5034182,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55534","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/.well-known/acme-challenge/BVg3p2QFuPxwLUVr8i45ofg1ysvW2Ol4qMzpTeNd-_8","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000045827,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849771.187544,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57446","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/.well-known/acme-challenge/BVg3p2QFuPxwLUVr8i45ofg1ysvW2Ol4qMzpTeNd-_8","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000040245,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849774.3293912,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57494","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/.well-known/acme-challenge/BVg3p2QFuPxwLUVr8i45ofg1ysvW2Ol4qMzpTeNd-_8","headers":{"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"]}},"bytes_read":0,"user_id":"","duration":0.000058382,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849827.0200696,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55510","client_ip":"**********","proto":"HTTP/1.1","method":"HEAD","host":"tankeprowlarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.00001181,"size":0,"status":308,"resp_headers":{"Server":["Caddy"],"Connection":["close"],"Location":["https://tankeprowlarr.duckdns.org/"],"Content-Type":[]}}
{"level":"error","ts":1750849828.4369676,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60100","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeprowlarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000717215,"size":36,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849828.498785,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60098","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/","headers":{"Accept-Encoding":["gzip"],"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeprowlarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000738485,"size":36,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849829.1801136,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60124","client_ip":"**********","proto":"HTTP/2.0","method":"HEAD","host":"tankeprowlarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"],"Referer":["http://tankeprowlarr.duckdns.org"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeprowlarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000675354,"size":36,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849840.2132585,"logger":"http.log.access.log4","msg":"handled request","request":{"remote_ip":"**********","remote_port":"34734","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeprowlarr.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeprowlarr.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000719763,"size":36,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
