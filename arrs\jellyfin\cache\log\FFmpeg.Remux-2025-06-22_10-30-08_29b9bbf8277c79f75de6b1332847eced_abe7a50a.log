{"Protocol":0,"Id":"29b9bbf8277c79f75de6b1332847eced","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 1/<PERSON> and <PERSON><PERSON><PERSON> - S01E06 - <PERSON> #9 HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":285146204,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S01E06 - <PERSON> #9 HDTV-720p","IsRemote":false,"ETag":"7fef9eede4d70e9e1046c9b804dc5894","RunTimeTicks":12360960000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1396453,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":98,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":112,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1845463,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E06 - Rick Potion #9 HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/68faae809bf248b64de429683c22da4e%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/68faae809bf248b64de429683c22da4e.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x557b34b13340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E06 - Rick Potion #9 HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Poción de Rick nº9
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Rick ayuda a Morty a conquistar a la chica de sus sueños.
    show            : Rick y Morty
    episode_id      : 6
    season_number   : 1
  Duration: 00:20:36.10, start: 0.000000, bitrate: 1845 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1396 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x557b34b18d40] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/68faae809bf248b64de429683c22da4e.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1396 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e0.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e1.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e2.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e3.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e4.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e5.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e6.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e7.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e8.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e9.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e10.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e11.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e12.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e13.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e14.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e15.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e16.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e17.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e18.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e19.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e20.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e21.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e22.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e23.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e24.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e25.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e26.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e27.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e28.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e29.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e30.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e31.ts' for writing
size=N/A time=00:02:12.12 bitrate=N/A speed= 132x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e32.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e33.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e34.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e35.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e36.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e37.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e38.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e39.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e40.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e41.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e42.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e43.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e44.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e45.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e46.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e47.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e48.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e49.ts' for writing
size=N/A time=00:04:03.13 bitrate=N/A speed= 162x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e50.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e51.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e52.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e53.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e54.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e55.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e56.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e57.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e58.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e59.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e60.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e61.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e62.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e63.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e64.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e65.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e66.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e67.ts' for writing
size=N/A time=00:05:48.00 bitrate=N/A speed= 174x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e68.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e69.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e70.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e71.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e72.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e73.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e74.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e75.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e76.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e77.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e78.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e79.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e80.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e81.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e82.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e83.ts' for writing
size=N/A time=00:07:24.83 bitrate=N/A speed= 178x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e84.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e85.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e86.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e87.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e88.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e89.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e90.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e91.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e92.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e93.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e94.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e95.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e96.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e97.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e98.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e99.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e100.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e101.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e102.ts' for writing
size=N/A time=00:09:21.82 bitrate=N/A speed= 187x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e103.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e104.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e105.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e106.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e107.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e108.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e109.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e110.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e111.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e112.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e113.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e114.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e115.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e116.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e117.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e118.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e119.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e120.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e121.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e122.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e123.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e124.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e125.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e126.ts' for writing
size=N/A time=00:11:45.82 bitrate=N/A speed= 202x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e127.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e128.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e129.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e130.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e131.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e132.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e133.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e134.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e135.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e136.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e137.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e138.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e139.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e140.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e141.ts' for writing
size=N/A time=00:13:21.53 bitrate=N/A speed= 200x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e142.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e143.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e144.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e145.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e146.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e147.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e148.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e149.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e150.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e151.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e152.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e153.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e154.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e155.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e156.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e157.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e158.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e159.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e160.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e161.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e162.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e163.ts' for writing
size=N/A time=00:15:33.63 bitrate=N/A speed= 207x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e164.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e165.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e166.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e167.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e168.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e169.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e170.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e171.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e172.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e173.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e174.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e175.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e176.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e177.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e178.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e179.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e180.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e181.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e182.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e183.ts' for writing
size=N/A time=00:17:26.91 bitrate=N/A speed= 209x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e184.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e185.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e186.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e187.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e188.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e189.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e190.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e191.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e192.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e193.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e194.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e195.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e196.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e197.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e198.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e199.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e200.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e201.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e202.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e203.ts' for writing
size=N/A time=00:19:26.81 bitrate=N/A speed= 212x    
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e204.ts' for writing
[hls @ 0x557b34b2c740] Opening '/cache/transcodes/68faae809bf248b64de429683c22da4e205.ts' for writing
[out#0/hls @ 0x557b34b18d40] video:210694KiB audio:19314KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:19:35.90 bitrate=N/A speed= 212x    