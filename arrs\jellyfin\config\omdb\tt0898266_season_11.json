{"Title": "The Big Bang Theory", "seriesID": "tt0898266", "Season": 11, "totalSeasons": 12, "Episodes": [{"Title": "The Proposal Proposal", "Year": "2017", "Rated": "TV-14", "Released": "25 Sep 2017", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> has trouble deciding whether or not she should marry <PERSON>. Meanwhile, <PERSON><PERSON><PERSON> confides in <PERSON> when she finds out some huge news.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMmE4MDc2NzctYWMzMy00MTJmLThjNzktZjNiZTA1NDI0MDVlXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.5", "imdbVotes": "3305", "imdbID": "tt6673774", "Episode": 1}, {"Title": "The Retraction Reaction", "Year": "2017", "Rated": "TV-14", "Released": "02 Oct 2017", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> angers the University and the Physics Community when he gives an embarrassing radio interview. <PERSON><PERSON><PERSON> and <PERSON> compare their successes at work.", "Language": "English, Hebrew, Hawaiian", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYjcwNDg1MDItZDUyNS00ODNlLWI4NGItNmViY2VkODc2OGU0XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2848", "imdbID": "tt6674506", "Episode": 2}, {"Title": "The Relaxation Integration", "Year": "2017", "Rated": "TV-14", "Released": "09 Oct 2017", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> struggle with picking a wedding date. Meanwhile, <PERSON> and <PERSON> fight over one of <PERSON><PERSON><PERSON>'s co-workers for a date.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNmY3ZmUxNDgtZDU2OC00ZmI1LThlMzAtN2UxOWM0ZDQ5M2E5XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.0", "imdbVotes": "2711", "imdbID": "tt6674504", "Episode": 3}, {"Title": "The Explosion Implosion", "Year": "2017", "Rated": "TV-PG", "Released": "16 Oct 2017", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON><PERSON><PERSON> are disappointed when they find out the sex of the baby. <PERSON> is annoyed when his mother befriends <PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNmRmN2UwMWQtYzlkZi00MDI4LWE5YmEtNDUzYWFlODY0YjY4XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2649", "imdbID": "tt6674498", "Episode": 4}, {"Title": "The Collaboration Contamination", "Year": "2017", "Rated": "TV-PG", "Released": "23 Oct 2017", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> work in the lab together causing <PERSON>, <PERSON><PERSON><PERSON> and <PERSON> to feel jealous and lonely. <PERSON> starts using a parenting book to control <PERSON>'s behavior.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYzdlMmRkZGUtZjg3Zi00MjdkLWE0MzMtZDRmZTU2MWJmNDJiXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.3", "imdbVotes": "2654", "imdbID": "tt6674496", "Episode": 5}, {"Title": "The Proton Regeneration", "Year": "2017", "Rated": "TV-PG", "Released": "02 Nov 2017", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON><PERSON> both go head-to-head for the role of the new Professor <PERSON><PERSON>, Meanwhile <PERSON> takes care of <PERSON><PERSON> when <PERSON><PERSON><PERSON> and <PERSON> are on bed rest.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNWZiOTg4M2QtM2I2Ny00NjNmLWJiODQtYmJhMWYyMTdjMDA3XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2709", "imdbID": "tt6674494", "Episode": 6}, {"Title": "The Geology Methodology", "Year": "2017", "Rated": "TV-PG", "Released": "09 Nov 2017", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "When <PERSON> asks <PERSON> to collaborate on a project with him, <PERSON> becomes worried about others finding out because he thinks geology is beneath him. <PERSON> seeks advice after reconnecting with <PERSON><PERSON>.", "Language": "English, Hindi", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMDA2MzdlMDYtNWEzYi00ZGJjLWFjZmMtZWRkMGI4MGU1NDEzXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.4", "imdbVotes": "2661", "imdbID": "tt6674492", "Episode": 7}, {"Title": "The Tesla Recoil", "Year": "2017", "Rated": "TV-PG", "Released": "16 Nov 2017", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> become upset after they discover that <PERSON> has been working with the military without telling them; <PERSON><PERSON><PERSON> asks <PERSON> to investigate her suspicions that <PERSON><PERSON> is attempting to take her job away from her.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNGUzZDcwNjQtMDhkNy00NGVhLWFlMDUtZWNjOGFiMGQ0YmU0XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2565", "imdbID": "tt6674490", "Episode": 8}, {"Title": "The Bitcoin Entanglement", "Year": "2017", "Rated": "TV-PG", "Released": "30 Nov 2017", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> tries to teach the other guys a lesson after they cut him out of a potentially valuable Bitcoin investment seven years ago.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYTM5MDVmYWYtM2FhNy00ZmVkLWIzMTQtODFhZDRiYzk5ZTI2XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.7", "imdbVotes": "2779", "imdbID": "tt6674486", "Episode": 9}, {"Title": "The Confidence Erosion", "Year": "2017", "Rated": "TV-PG", "Released": "07 Dec 2017", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> try to eliminate stress from wedding planning by applying math to the process. <PERSON> and <PERSON> come to blows when <PERSON> begins to feel like <PERSON> hurts his confidence.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BODk5YTBiMTctY2ZlYS00MjE0LWFiMDktMjIyOGVjMTVjNjYyXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.3", "imdbVotes": "2582", "imdbID": "tt6674480", "Episode": 10}, {"Title": "The Celebration Reverberation", "Year": "2017", "Rated": "TV-14", "Released": "14 Dec 2017", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON>'s feud continues as the gang plan <PERSON><PERSON>'s first birthday party. <PERSON>'s surprise for <PERSON>'s birthday goes awry when they get food poisoning.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMGQ1Yzk4ZDAtZDc0YS00NDJlLWJkYjItZWI0ZjRkN2Y2ZGQwXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2630", "imdbID": "tt6674476", "Episode": 11}, {"Title": "The Matrimonial Metric", "Year": "2018", "Rated": "TV-PG", "Released": "04 Jan 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> pick their best-man and maid of honor by experimenting on the gang. Meanwhile, <PERSON>'s self-confidence is still badly damaged after reading his brother's Christmas letter.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNmFiNmFmMGEtMDIzNy00ZGIyLTkzZDAtOGNlYjQ5MDkxMzIyXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.4", "imdbVotes": "2580", "imdbID": "tt6674472", "Episode": 12}, {"Title": "The Solo Oscillation", "Year": "2018", "Rated": "TV-PG", "Released": "11 Jan 2018", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> kicks <PERSON> out of the apartment to work solo, and she and <PERSON> end up bonding during a series of science experiments; <PERSON> is replaced by <PERSON> in Footprints on the Moon.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNjBhZWY4MGItMzQyOS00ZGVjLWE2MzctNjliNTQyYzg1NTZjXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2606", "imdbID": "tt6674468", "Episode": 13}, {"Title": "The Separation Triangulation", "Year": "2018", "Rated": "TV-PG", "Released": "18 Jan 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> meets a girl at the Planetarium, but soon after finds out she is married. <PERSON> rents out his old room to use for his work.", "Language": "English, Hindi", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNTNjMzViOWUtNzZmNS00NWY3LTgzYmQtMmQwZTA0MDhhODlmXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2660", "imdbID": "tt6674466", "Episode": 14}, {"Title": "The Novelization Correlation", "Year": "2018", "Rated": "TV-PG", "Released": "01 Feb 2018", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON>'s book raises questions, and <PERSON> guest stars on the new <PERSON> <PERSON><PERSON> show.", "Language": "English, Hindi, Hebrew", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZGNlYzZkNzItMzE4NC00NjZlLWJkZDgtNmNiMGM0NDUwZDVhXkEyXkFqcGdeQXVyMjYxMDY3NjA@._V1_SX300.jpg", "imdbRating": "7.5", "imdbVotes": "2482", "imdbID": "tt6674462", "Episode": 15}, {"Title": "The Neonatal Nomenclature", "Year": "2018", "Rated": "TV-PG", "Released": "01 Mar 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "When <PERSON><PERSON><PERSON> won't go into labor, the gang try different tactics to get things started. Also, <PERSON><PERSON><PERSON> confronts <PERSON><PERSON><PERSON> after <PERSON> accidentally reveals she's already chosen their son's name.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZjY2MGYxYjgtZDE5Ny00ZDhlLWI4MGUtOTI3NGFlOGNiZWJjXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2449", "imdbID": "tt6674448", "Episode": 16}, {"Title": "The Athenaeum Allocation", "Year": "2018", "Rated": "TV-PG", "Released": "08 Mar 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> jumps through hoops to help secure the perfect wedding venue for <PERSON> and <PERSON>. Also, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> have a hard time deciding who should stay at home with the kids and who should go back to work.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNTIwZWU3YzktZDQwNy00ZjA1LWJlOGQtNGNlY2E5YzY1OWViXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "6.8", "imdbVotes": "2489", "imdbID": "tt6674452", "Episode": 17}, {"Title": "The Gates Excitation", "Year": "2018", "Rated": "TV-PG", "Released": "29 Mar 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON>, <PERSON> and <PERSON> try to meet <PERSON>, who's meeting with <PERSON> to merge their companies; <PERSON> thinks he's the victim of an April Fool's prank.", "Language": "English, Spanish", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMjQwODk0NzY2OV5BMl5BanBnXkFtZTgwNzM2NjUxNTM@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2553", "imdbID": "tt6674456", "Episode": 18}, {"Title": "The Tenant Disassociation", "Year": "2018", "Rated": "TV-PG", "Released": "05 Apr 2018", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> runs against <PERSON> to be president of the tenants association; <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> find a drone in the backyard.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZWNhMDRkYjctN2JjMi00NzkwLTk1YWYtOTY0Y2FjZWMxNmM1XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2461", "imdbID": "tt6674454", "Episode": 19}, {"Title": "The Reclusive Potential", "Year": "2018", "Rated": "TV-PG", "Released": "12 Apr 2018", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> takes the guys to a cabin in the middle of nowhere to meet a reclusive scientist; <PERSON> and <PERSON><PERSON><PERSON> improvise <PERSON>'s bachelorette party.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZDljMTJiM2EtNTJjMi00NjdjLWFhODYtZWFlZDRjZTM1MTQyXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2489", "imdbID": "tt6674434", "Episode": 20}, {"Title": "The Comet Polarization", "Year": "2018", "Rated": "TV-PG", "Released": "19 Apr 2018", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON>'s comic book store experience changes when writer <PERSON> puts <PERSON>'s store on the map; friendships are threatened when <PERSON><PERSON><PERSON><PERSON><PERSON> takes credit for <PERSON>'s astronomical discovery.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYTg2NTZmM2QtZWRiMi00MGQyLWJjMTgtZjFlNDdjZWFhZDEzXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.4", "imdbVotes": "2538", "imdbID": "tt6674432", "Episode": 21}, {"Title": "The Monetary Insufficiency", "Year": "2018", "Rated": "TV-PG", "Released": "26 Apr 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> goes to Vegas to win money for science. Also, <PERSON> and <PERSON><PERSON><PERSON> take <PERSON> wedding dress shopping, but her terrible choice entangles them in a web of lies.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYzRmYzZiZTEtMTVjZi00ODRiLTg0NzEtZjk3MDBiMDgzZjdkXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2468", "imdbID": "tt6674428", "Episode": 22}, {"Title": "The Sibling Realignment", "Year": "2018", "Rated": "TV-PG", "Released": "03 May 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> make a road trip to wrangle a reluctant wedding guest.", "Language": "English, German", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZTEwNjY2OGYtNjFjOS00NTcwLWI4OWMtMjhiNjBjZGY5MmY5XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.7", "imdbVotes": "2629", "imdbID": "tt6674424", "Episode": 23}, {"Title": "The Bow Tie Asymmetry", "Year": "2018", "Rated": "TV-PG", "Released": "10 May 2018", "Runtime": "22 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "When <PERSON>'s parents and <PERSON>'s family arrive, everybody is focused to make sure all wedding arrangements go according to plan - everyone except the bride and groom.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BOWVjMzRkOWYtZWE4YS00MzcwLTg2M2EtZjQ1MWM2Y2NlZDJmXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.0", "imdbVotes": "4466", "imdbID": "tt6674430", "Episode": 24}], "Response": "True"}