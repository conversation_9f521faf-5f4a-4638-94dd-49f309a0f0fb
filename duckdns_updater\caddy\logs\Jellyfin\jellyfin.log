{"level":"info","ts":1750849771.3316,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57454","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeflix.duckdns.org","uri":"/.well-known/acme-challenge/cEzR9Gvyc3nDtgRVnVUKIZvkj1pisjNMDhOOqA5C188","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.00004437,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849772.391189,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57468","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeflix.duckdns.org","uri":"/.well-known/acme-challenge/cEzR9Gvyc3nDtgRVnVUKIZvkj1pisjNMDhOOqA5C188","headers":{"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"]}},"bytes_read":0,"user_id":"","duration":0.0000427,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849772.5843344,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"57482","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeflix.duckdns.org","uri":"/.well-known/acme-challenge/cEzR9Gvyc3nDtgRVnVUKIZvkj1pisjNMDhOOqA5C188","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000044894,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849781.5269005,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48602","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeflix.duckdns.org","uri":"/.well-known/acme-challenge/cEzR9Gvyc3nDtgRVnVUKIZvkj1pisjNMDhOOqA5C188","headers":{"Accept-Encoding":["gzip"],"Connection":["close"],"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"]}},"bytes_read":0,"user_id":"","duration":0.000048453,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"info","ts":1750849781.844743,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"48624","client_ip":"**********","proto":"HTTP/1.1","method":"GET","host":"tankeflix.duckdns.org","uri":"/.well-known/acme-challenge/cEzR9Gvyc3nDtgRVnVUKIZvkj1pisjNMDhOOqA5C188","headers":{"User-Agent":["Mozilla/5.0 (compatible; Let's Encrypt validation server; +https://www.letsencrypt.org)"],"Accept":["*/*"],"Accept-Encoding":["gzip"],"Connection":["close"]}},"bytes_read":0,"user_id":"","duration":0.000062103,"size":87,"status":200,"resp_headers":{"Server":["Caddy"],"Content-Type":["text/plain"]}}
{"level":"error","ts":1750849788.7341883,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55294","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeflix.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeflix.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.00071084,"size":36,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849794.2238955,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"55320","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeflix.duckdns.org","uri":"/web/serviceworker.js","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip, deflate, br, zstd"],"Sec-Fetch-Site":["same-origin"],"Service-Worker":["script"],"Referer":["https://tankeflix.duckdns.org/web/serviceworker.js"],"Priority":["u=4, i"],"Cache-Control":["max-age=0"],"Accept":["*/*"],"Sec-Fetch-Dest":["serviceworker"],"Accept-Language":["es-ES,es;q=0.9,en;q=0.8"],"Sec-Fetch-Mode":["same-origin"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeflix.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000953074,"size":36,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
{"level":"error","ts":1750849802.5401266,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"49262","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeflix.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeflix.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.000671845,"size":36,"status":503,"resp_headers":{"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"],"Content-Type":["text/plain; charset=utf-8"]}}
{"level":"error","ts":1750849861.1062253,"logger":"http.log.access.log0","msg":"handled request","request":{"remote_ip":"**********","remote_port":"60098","client_ip":"**********","proto":"HTTP/2.0","method":"GET","host":"tankeflix.duckdns.org","uri":"/","headers":{"User-Agent":["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"Accept-Encoding":["gzip"]},"tls":{"resumed":false,"version":772,"cipher_suite":4865,"proto":"h2","server_name":"tankeflix.duckdns.org"}},"bytes_read":0,"user_id":"","duration":0.001040336,"size":36,"status":503,"resp_headers":{"Content-Type":["text/plain; charset=utf-8"],"Server":["Caddy"],"Alt-Svc":["h3=\":443\"; ma=2592000"]}}
