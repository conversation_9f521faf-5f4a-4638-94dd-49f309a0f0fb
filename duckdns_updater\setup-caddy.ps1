# Script de configuración para integrar Caddy en DuckDNS existente
# Ejecutar desde el directorio duckdns_updater

Write-Host "=== INTEGRACIÓN DE CADDY EN DUCKDNS EXISTENTE ===" -ForegroundColor Green

# Verificar que estamos en el directorio correcto
if (!(Test-Path "docker-compose.yml")) {
    Write-Host "✗ No se encontró docker-compose.yml en el directorio actual" -ForegroundColor Red
    Write-Host "Asegúrate de ejecutar este script desde el directorio duckdns_updater" -ForegroundColor Yellow
    exit 1
}

# Verificar Docker
Write-Host "Verificando Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker encontrado: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker no está instalado o no está en el PATH" -ForegroundColor Red
    exit 1
}

# Verificar Docker Compose
try {
    $composeVersion = docker compose version
    Write-Host "✓ Docker Compose encontrado: $composeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker Compose no está disponible" -ForegroundColor Red
    exit 1
}

# Verificar que existe el Caddyfile adaptado
if (!(Test-Path "../Caddyfile.host")) {
    Write-Host "✗ No se encontró Caddyfile.host en el directorio padre" -ForegroundColor Red
    Write-Host "Este archivo debería haber sido creado automáticamente" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "✓ Caddyfile.host encontrado" -ForegroundColor Green
}

# Verificar directorio de logs
$logDir = "C:\Cosas\Caddy\Logs"
if (!(Test-Path $logDir)) {
    Write-Host "⚠ Directorio de logs no encontrado: $logDir" -ForegroundColor Yellow
    Write-Host "¿Quieres crearlo? (S/N): " -ForegroundColor Yellow -NoNewline
    $response = Read-Host
    if ($response -eq "S" -or $response -eq "s") {
        try {
            New-Item -ItemType Directory -Path $logDir -Force -Recurse | Out-Null
            Write-Host "✓ Directorio de logs creado: $logDir" -ForegroundColor Green
        } catch {
            Write-Host "✗ No se pudo crear el directorio de logs" -ForegroundColor Red
            Write-Host "Puedes cambiarlo en el docker-compose.yml si prefieres otra ubicación" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "✓ Directorio de logs encontrado: $logDir" -ForegroundColor Green
}

# Crear subdirectorios de logs si no existen
$logSubdirs = @("Jellyfin", "Qbittorrent", "Sonarr", "Jackett", "Prowlarr", "Radarr", "Jellyseerr")
foreach ($subdir in $logSubdirs) {
    $fullPath = Join-Path $logDir $subdir
    if (!(Test-Path $fullPath)) {
        try {
            New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
            Write-Host "✓ Creado subdirectorio de logs: $subdir" -ForegroundColor Green
        } catch {
            Write-Host "⚠ No se pudo crear subdirectorio: $subdir" -ForegroundColor Yellow
        }
    }
}

# Verificar servicios del host
Write-Host "`nVerificando servicios del host..." -ForegroundColor Yellow
$services = @{
    "Jellyfin" = 8096
    "qBittorrent" = 8091
    "Sonarr" = 8989
    "Jackett" = 9117
    "Prowlarr" = 9696
    "Radarr" = 7878
    "Jellyseerr" = 5055
}

$runningServices = 0
foreach ($service in $services.GetEnumerator()) {
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $service.Value -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✓ $($service.Key) ejecutándose en puerto $($service.Value)" -ForegroundColor Green
            $runningServices++
        } else {
            Write-Host "⚠ $($service.Key) no accesible en puerto $($service.Value)" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠ No se pudo verificar $($service.Key) en puerto $($service.Value)" -ForegroundColor Yellow
    }
}

if ($runningServices -eq 0) {
    Write-Host "`n⚠ No se detectaron servicios ejecutándose" -ForegroundColor Yellow
    Write-Host "Asegúrate de que tus servicios (Jellyfin, qBittorrent, etc.) estén ejecutándose antes de iniciar Caddy" -ForegroundColor Yellow
}

# Verificar configuración DuckDNS
Write-Host "`nVerificando configuración DuckDNS..." -ForegroundColor Yellow
$composeContent = Get-Content "docker-compose.yml" -Raw

if ($composeContent -match "TOKEN=([a-f0-9-]{36})") {
    Write-Host "✓ Tokens DuckDNS configurados" -ForegroundColor Green
} else {
    Write-Host "⚠ Verifica que los tokens DuckDNS sean correctos" -ForegroundColor Yellow
}

Write-Host "`n=== CONFIGURACIÓN COMPLETADA ===" -ForegroundColor Green
Write-Host ""
Write-Host "Tu configuración DuckDNS existente se ha mantenido intacta." -ForegroundColor White
Write-Host "Se ha agregado Caddy como reverse proxy." -ForegroundColor White
Write-Host ""
Write-Host "Próximos pasos:" -ForegroundColor Yellow
Write-Host "1. Asegúrate de que tus servicios estén ejecutándose en el host" -ForegroundColor White
Write-Host "2. Ejecuta: docker compose up -d" -ForegroundColor White
Write-Host "3. Verifica los logs: docker compose logs -f caddy" -ForegroundColor White
Write-Host "4. Prueba acceder a tus dominios DuckDNS" -ForegroundColor White
Write-Host ""
Write-Host "Comandos útiles:" -ForegroundColor Yellow
Write-Host "- Ver estado: docker compose ps" -ForegroundColor White
Write-Host "- Ver logs de Caddy: docker compose logs -f caddy" -ForegroundColor White
Write-Host "- Ver logs de DuckDNS: docker compose logs -f duckdns_jucago705" -ForegroundColor White
Write-Host "- Detener servicios: docker compose down" -ForegroundColor White
Write-Host "- Reiniciar solo Caddy: docker compose restart caddy" -ForegroundColor White
