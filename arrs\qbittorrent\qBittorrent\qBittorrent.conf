[Application]
FileLogger\Age=1
FileLogger\AgeType=1
FileLogger\Backup=true
FileLogger\DeleteOld=true
FileLogger\Enabled=true
FileLogger\MaxSizeBytes=66560
FileLogger\Path=/config/qBittorrent/logs

[AutoRun]
enabled=false
program=

[BitTorrent]
Session\AddTorrentStopped=false
Session\DefaultSavePath=/CONTENIDO/Descargas
Session\ExcludedFileNames=
Session\MaxActiveDownloads=10000
Session\MaxActiveTorrents=10000
Session\MaxActiveUploads=5
Session\MaxConnections=-1
Session\MaxConnectionsPerTorrent=-1
Session\MaxUploads=-1
Session\MaxUploadsPerTorrent=-1
Session\Port=6881
Session\QueueingSystemEnabled=true
Session\SSL\Port=44822
Session\ShareLimitAction=Stop
Session\Tags=tv-sonarr
Session\TempPath=/downloads/incomplete/

[Core]
AutoDeleteAddedTorrentFile=Never

[LegalNotice]
Accepted=true

[Meta]
MigrationVersion=8

[Network]
PortForwardingEnabled=false
Proxy\HostnameLookupEnabled=false
Proxy\Profiles\BitTorrent=true
Proxy\Profiles\Misc=true
Proxy\Profiles\RSS=true

[Preferences]
Connection\PortRangeMin=6881
Connection\UPnP=false
Downloads\SavePath=/downloads/
Downloads\TempPath=/downloads/incomplete/
General\Locale=es
MailNotification\req_auth=true
WebUI\Address=*
WebUI\AuthSubnetWhitelist=@Invalid()
WebUI\BanDuration=600
WebUI\HostHeaderValidation=false
WebUI\MaxAuthenticationFailCount=2
WebUI\Password_PBKDF2="@ByteArray(mCylVUi5AOvjwNEtc64jmg==:fcpnNChFt+kHVvIcwCQv/v3P9h13OYDCUf9oOx/SK+Mq9j37X3pPfeudl17JcESpfOZcA54lSfCNupJulWKEgg==)"
WebUI\Port=8091
WebUI\ServerDomains=
WebUI\Username=Tankeeee2_GAMES

[RSS]
AutoDownloader\DownloadRepacks=true
AutoDownloader\SmartEpisodeFilter=s(\\d+)e(\\d+), (\\d+)x(\\d+), "(\\d{4}[.\\-]\\d{1,2}[.\\-]\\d{1,2})", "(\\d{1,2}[.\\-]\\d{1,2}[.\\-]\\d{4})"
