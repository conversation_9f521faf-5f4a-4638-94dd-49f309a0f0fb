{"Protocol":0,"Id":"14857cd781a7139ab1db7a466b13d843","Path":"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":34844647663,"Name":"How to Train Your Dragon 2 (2014) Remux-2160p","IsRemote":false,"ETag":"74c318e50ca08b9347dd37485593860d","RunTimeTicks":61146310000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"hevc","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt2020nc","ColorTransfer":"smpte2084","ColorPrimaries":"bt2020","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":2,"VideoRangeType":2,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"4K HEVC HDR","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":45588553,"BitDepth":10,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main 10","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p10le","Level":153,"IsAnamorphic":false},{"Codec":"dts","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - DTS - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":768000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"ac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"dts","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":2,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - DTS-HD MA \u002B DTS:X - 7.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"7.1","BitRate":6552825,"BitDepth":24,"RefFrames":null,"PacketLength":null,"Channels":8,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS-HD MA \u002B DTS:X","Type":0,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Forzados","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Forzados - Spanish - Predeterminado - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"PGSSUB","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - PGSSUB","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":6,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":53549378,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G  -i file:"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 14232000 -bufsize 28464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 "expr:gte(t,n_forced*3)" -sc_threshold:v:0 0 -vf "setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,2560)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/3ea13e46df7990deade63628f239e8fa%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/3ea13e46df7990deade63628f239e8fa.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, matroska,webm, from 'file:/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv':
  Metadata:
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2019-01-10T18:03:34.000000Z
  Duration: 01:41:54.63, start: 0.000000, bitrate: 45588 kb/s
  Chapters:
    Chapter #0:0: start 0.000000, end 211.920022
      Metadata:
        title           : Chapter 01
    Chapter #0:1: start 211.920022, end 324.615956
      Metadata:
        title           : Chapter 02
    Chapter #0:2: start 324.615956, end 469.927778
      Metadata:
        title           : Chapter 03
    Chapter #0:3: start 469.927778, end 619.952667
      Metadata:
        title           : Chapter 04
    Chapter #0:4: start 619.952667, end 800.257778
      Metadata:
        title           : Chapter 05
    Chapter #0:5: start 800.257778, end 1022.980289
      Metadata:
        title           : Chapter 06
    Chapter #0:6: start 1022.980289, end 1205.621067
      Metadata:
        title           : Chapter 07
    Chapter #0:7: start 1205.621067, end 1427.175733
      Metadata:
        title           : Chapter 08
    Chapter #0:8: start 1427.175733, end 1638.386733
      Metadata:
        title           : Chapter 09
    Chapter #0:9: start 1638.386733, end 1765.388622
      Metadata:
        title           : Chapter 10
    Chapter #0:10: start 1765.388622, end 2025.857156
      Metadata:
        title           : Chapter 11
    Chapter #0:11: start 2025.857156, end 2229.393822
      Metadata:
        title           : Chapter 12
    Chapter #0:12: start 2229.393822, end 2350.890200
      Metadata:
        title           : Chapter 13
    Chapter #0:13: start 2350.890200, end 2463.085622
      Metadata:
        title           : Chapter 14
    Chapter #0:14: start 2463.085622, end 2707.913533
      Metadata:
        title           : Chapter 15
    Chapter #0:15: start 2707.913533, end 2840.546022
      Metadata:
        title           : Chapter 16
    Chapter #0:16: start 2840.546022, end 3115.445667
      Metadata:
        title           : Chapter 17
    Chapter #0:17: start 3115.445667, end 3279.651356
      Metadata:
        title           : Chapter 18
    Chapter #0:18: start 3279.651356, end 3379.918200
      Metadata:
        title           : Chapter 19
    Chapter #0:19: start 3379.918200, end 3637.091778
      Metadata:
        title           : Chapter 20
    Chapter #0:20: start 3637.091778, end 3872.702156
      Metadata:
        title           : Chapter 21
    Chapter #0:21: start 3872.702156, end 3998.869867
      Metadata:
        title           : Chapter 22
    Chapter #0:22: start 3998.869867, end 4188.058867
      Metadata:
        title           : Chapter 23
    Chapter #0:23: start 4188.058867, end 4445.023911
      Metadata:
        title           : Chapter 24
    Chapter #0:24: start 4445.023911, end 4719.047667
      Metadata:
        title           : Chapter 25
    Chapter #0:25: start 4719.047667, end 4871.074533
      Metadata:
        title           : Chapter 26
    Chapter #0:26: start 4871.074533, end 4978.974000
      Metadata:
        title           : Chapter 27
    Chapter #0:27: start 4978.974000, end 5073.985578
      Metadata:
        title           : Chapter 28
    Chapter #0:28: start 5073.985578, end 5288.574956
      Metadata:
        title           : Chapter 29
    Chapter #0:29: start 5288.574956, end 5467.420289
      Metadata:
        title           : Chapter 30
    Chapter #0:30: start 5467.420289, end 5545.915356
      Metadata:
        title           : Chapter 31
    Chapter #0:31: start 5545.915356, end 6114.631000
      Metadata:
        title           : Chapter 32
  Stream #0:0: Video: hevc (Main 10), yuv420p10le(tv, bt2020nc/bt2020/smpte2084), 3840x2160 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 37576891
        DURATION-eng    : 01:41:54.609000000
        NUMBER_OF_FRAMES-eng: 146604
        NUMBER_OF_BYTES-eng: 28721000090
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: dts (dca) (DTS), 48000 Hz, 5.1(side), fltp, 768 kb/s (default) (forced)
      Metadata:
        BPS-eng         : 767999
        DURATION-eng    : 01:41:54.614000000
        NUMBER_OF_FRAMES-eng: 573245
        NUMBER_OF_BYTES-eng: 587002880
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(spa): Audio: ac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        BPS-eng         : 640000
        DURATION-eng    : 01:41:54.624000000
        NUMBER_OF_FRAMES-eng: 191082
        NUMBER_OF_BYTES-eng: 489169920
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(eng): Audio: dts (dca) (DTS-HD MA + DTS:X), 48000 Hz, 7.1, s32p (24 bit)
      Metadata:
        BPS-eng         : 6552825
        DURATION-eng    : 01:41:54.614000000
        NUMBER_OF_FRAMES-eng: 573245
        NUMBER_OF_BYTES-eng: 5008499676
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(spa): Subtitle: subrip (srt) (default) (forced)
      Metadata:
        title           : Forzados
        BPS-eng         : 0
        DURATION-eng    : 00:09:24.813000000
        NUMBER_OF_FRAMES-eng: 2
        NUMBER_OF_BYTES-eng: 46
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:5(spa): Subtitle: subrip (srt)
      Metadata:
        BPS-eng         : 43
        DURATION-eng    : 01:40:34.444000000
        NUMBER_OF_FRAMES-eng: 1146
        NUMBER_OF_BYTES-eng: 33098
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:6(eng): Subtitle: hdmv_pgs_subtitle (pgssub), 1920x1080
      Metadata:
        BPS-eng         : 45999
        DURATION-eng    : 01:40:44.163000000
        NUMBER_OF_FRAMES-eng: 3870
        NUMBER_OF_BYTES-eng: 34753782
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
[tonemapx @ 0x5580c8b59700] Using CPU capabilities: AVX2 FMA3
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (dts (dca) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[tonemapx @ 0x7f84a0012140] Using CPU capabilities: AVX2 FMA3
[libx264 @ 0x5580c8aeafc0] using SAR=1/1
[libx264 @ 0x5580c8aeafc0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x5580c8aeafc0] profile High, level 5.0, 4:2:0, 8-bit
[libx264 @ 0x5580c8aeafc0] 264 - core 164 r3095 baee400 - H.264/MPEG-4 AVC codec - Copyleft 2003-2022 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=0 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1 trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=18 lookahead_threads=3 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=23 scenecut=0 intra_refresh=0 rc_lookahead=10 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 vbv_maxrate=14232 vbv_bufsize=28464 crf_max=0.0 nal_hrd=none filler=0 ip_ratio=1.40 aq=1:1.00
Output #0, hls, to '/cache/transcodes/3ea13e46df7990deade63628f239e8fa.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264, yuv420p(tv, bt709, progressive), 2560x1440 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 90k tbn (default)
      Metadata:
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 14232000/0/0 buffer size: 28464000 vbv_delay: N/A
  Stream #0:1: Audio: aac, 48000 Hz, 5.1, s16, 640 kb/s (default) (forced)
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=   18 fps=9.0 q=28.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   35 fps= 14 q=28.0 size=N/A time=00:00:00.70 bitrate=N/A speed=0.284x    
frame=   52 fps= 17 q=28.0 size=N/A time=00:00:01.41 bitrate=N/A speed=0.473x    
[hls @ 0x5580c8b33200] Opening '/cache/transcodes/3ea13e46df7990deade63628f239e8fa0.ts' for writing
frame=   75 fps= 21 q=28.0 size=N/A time=00:00:02.37 bitrate=N/A speed=0.679x    

[q] command received. Exiting.

[hls @ 0x5580c8b33200] Opening '/cache/transcodes/3ea13e46df7990deade63628f239e8fa1.ts' for writing
[hls @ 0x5580c8b33200] Opening '/cache/transcodes/3ea13e46df7990deade63628f239e8fa2.ts' for writing
[out#0/hls @ 0x5580ce3cc580] video:2932KiB audio:510KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=  156 fps= 33 q=-1.0 Lsize=N/A time=00:00:05.75 bitrate=N/A dup=1 drop=0 speed=1.23x    
[libx264 @ 0x5580c8aeafc0] frame I:3     Avg QP:14.23  size: 79612
[libx264 @ 0x5580c8aeafc0] frame P:40    Avg QP:18.75  size: 39653
[libx264 @ 0x5580c8aeafc0] frame B:113   Avg QP:19.93  size: 10409
[libx264 @ 0x5580c8aeafc0] consecutive B-frames:  1.9%  2.6%  5.8% 89.7%
[libx264 @ 0x5580c8aeafc0] mb I  I16..4: 64.8% 27.9%  7.3%
[libx264 @ 0x5580c8aeafc0] mb P  I16..4: 16.9% 20.3%  1.9%  P16..4:  9.2%  2.5%  0.4%  0.0%  0.0%    skip:48.7%
[libx264 @ 0x5580c8aeafc0] mb B  I16..4:  2.1%  1.7%  0.0%  B16..8: 11.7%  2.9%  0.1%  direct: 5.8%  skip:75.8%  L0:47.8% L1:41.0% BI:11.2%
[libx264 @ 0x5580c8aeafc0] 8x8 transform intra:47.4% inter:38.3%
[libx264 @ 0x5580c8aeafc0] coded y,uvDC,uvAC intra: 40.7% 45.9% 17.9% inter: 2.0% 8.5% 0.1%
[libx264 @ 0x5580c8aeafc0] i16 v,h,dc,p: 41% 28% 18% 13%
[libx264 @ 0x5580c8aeafc0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu:  7% 31% 22%  7%  5%  3% 11%  3% 11%
[libx264 @ 0x5580c8aeafc0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu:  7% 47%  9%  5%  5%  2% 13%  2% 10%
[libx264 @ 0x5580c8aeafc0] i8c dc,h,v,p: 48% 33% 11%  8%
[libx264 @ 0x5580c8aeafc0] Weighted P-Frames: Y:0.0% UV:0.0%
[libx264 @ 0x5580c8aeafc0] kb/s:3690.10