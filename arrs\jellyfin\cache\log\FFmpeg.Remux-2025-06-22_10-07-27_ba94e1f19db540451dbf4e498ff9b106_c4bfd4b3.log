{"Protocol":0,"Id":"ba94e1f19db540451dbf4e498ff9b106","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 2/<PERSON> and <PERSON><PERSON><PERSON> - S02E08 - Interdimensional Cable 2 - Tempting Fate HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":283559803,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S02E08 - Interdimensional Cable 2 - Tempting Fate HDTV-720p","IsRemote":false,"ETag":"72909b232f1876ff5081b8199fdf1720","RunTimeTicks":12950080000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1314121,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":117,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":104,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1751709,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E08 - Interdimensional Cable 2 - Tempting Fate HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/4fc29f30d90f0c004332b81d05d92360%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/4fc29f30d90f0c004332b81d05d92360.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x5629baabe340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E08 - Interdimensional Cable 2 - Tempting Fate HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Cable interdimensional 2: tentando al destino
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Jerry tomará a una difícil decisión para salvarle la vida a un líder alienígena. Rick, Morty y Summer exploran la televisión interdimensional.
    show            : Rick y Morty
    episode_id      : 8
    season_number   : 2
  Duration: 00:21:35.01, start: 0.000000, bitrate: 1751 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1314 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x5629bab2e640] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1314 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923600.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923601.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923602.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923603.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923604.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923605.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923606.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923607.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923608.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d923609.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236010.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236011.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236012.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236013.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236014.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236015.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236016.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236017.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236018.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236019.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236020.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236021.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236022.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236023.ts' for writing
size=N/A time=00:01:26.52 bitrate=N/A speed=86.5x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236024.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236025.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236026.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236027.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236028.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236029.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236030.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236031.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236032.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236033.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236034.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236035.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236036.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236037.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236038.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236039.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236040.ts' for writing
size=N/A time=00:03:13.98 bitrate=N/A speed= 129x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236041.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236042.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236043.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236044.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236045.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236046.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236047.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236048.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236049.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236050.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236051.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236052.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236053.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236054.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236055.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236056.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236057.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236058.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236059.ts' for writing
size=N/A time=00:05:05.72 bitrate=N/A speed= 153x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236060.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236061.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236062.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236063.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236064.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236065.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236066.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236067.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236068.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236069.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236070.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236071.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236072.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236073.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236074.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236075.ts' for writing
size=N/A time=00:06:38.52 bitrate=N/A speed= 159x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236076.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236077.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236078.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236079.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236080.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236081.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236082.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236083.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236084.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236085.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236086.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236087.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236088.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236089.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236090.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236091.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236092.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236093.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236094.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236095.ts' for writing
size=N/A time=00:08:42.33 bitrate=N/A speed= 174x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236096.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236097.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236098.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d9236099.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360100.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360101.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360102.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360103.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360104.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360105.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360106.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360107.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360108.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360109.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360110.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360111.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360112.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360113.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360114.ts' for writing
size=N/A time=00:10:35.80 bitrate=N/A speed= 182x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360115.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360116.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360117.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360118.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360119.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360120.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360121.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360122.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360123.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360124.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360125.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360126.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360127.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360128.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360129.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360130.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360131.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360132.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360133.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360134.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360135.ts' for writing
size=N/A time=00:12:40.57 bitrate=N/A speed= 190x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360136.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360137.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360138.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360139.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360140.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360141.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360142.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360143.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360144.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360145.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360146.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360147.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360148.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360149.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360150.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360151.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360152.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360153.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360154.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360155.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360156.ts' for writing
size=N/A time=00:14:44.57 bitrate=N/A speed= 197x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360157.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360158.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360159.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360160.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360161.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360162.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360163.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360164.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360165.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360166.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360167.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360168.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360169.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360170.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360171.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360172.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360173.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360174.ts' for writing
size=N/A time=00:16:33.50 bitrate=N/A speed= 199x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360175.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360176.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360177.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360178.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360179.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360180.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360181.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360182.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360183.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360184.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360185.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360186.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360187.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360188.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360189.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360190.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360191.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360192.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360193.ts' for writing
size=N/A time=00:18:28.57 bitrate=N/A speed= 202x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360194.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360195.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360196.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360197.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360198.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360199.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360200.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360201.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360202.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360203.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360204.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360205.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360206.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360207.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360208.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360209.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360210.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360211.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360212.ts' for writing
size=N/A time=00:20:26.52 bitrate=N/A speed= 204x    
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360213.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360214.ts' for writing
[hls @ 0x5629bab1aa40] Opening '/cache/transcodes/4fc29f30d90f0c004332b81d05d92360215.ts' for writing
[out#0/hls @ 0x5629bab2e640] video:207722KiB audio:20234KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:20:37.35 bitrate=N/A speed= 204x    