# Integración de Caddy con DuckDNS Existente

Esta configuración integra Caddy como reverse proxy en tu setup existente de DuckDNS **sin modificar** tu configuración actual que ya funciona.

## 🎯 Qué se ha hecho

✅ **Se mantiene intacta tu configuración DuckDNS existente**
- Los servicios `duckdns_jucago705` y `duckdns_jucago706` siguen exactamente igual
- Los tokens y dominios no han cambiado
- La estructura de directorios se mantiene

✅ **Se agrega Caddy como reverse proxy**
- Usa tu configuración actual de Caddyfile (adaptada para Docker)
- Mantiene todos tus headers de seguridad y configuraciones SSL
- Conserva la estructura de logs en `C:\Cosas\Caddy\Logs`

## 📁 Archivos Modificados/Creados

### Modificado
- `docker-compose.yml` - Se agregó el servicio Caddy manteniendo DuckDNS intacto

### Creados
- `../Caddyfile.host` - Tu Caddyfile adaptado para Docker (mantiene toda tu configuración)
- `setup-caddy.ps1` - Script de configuración y verificación
- `README-CADDY.md` - Esta documentación

## 🚀 Cómo usar

### 1. Configuración inicial
```powershell
# Desde el directorio duckdns_updater
.\setup-caddy.ps1
```

### 2. Iniciar servicios
```bash
# Desde el directorio duckdns_updater
docker compose up -d
```

### 3. Verificar funcionamiento
```bash
# Ver estado de todos los servicios
docker compose ps

# Ver logs de Caddy
docker compose logs -f caddy

# Ver logs de DuckDNS
docker compose logs -f duckdns_jucago705
```

## 🔧 Configuración Técnica

### Servicios Configurados
- **Caddy**: Puerto 80 (HTTP) y 443 (HTTPS)
- **DuckDNS**: Actualización automática de DNS (sin cambios)

### Dominios y Puertos (mantenidos de tu configuración original)
- `tankeflix.duckdns.org` → localhost:8096 (Jellyfin)
- `tanketorrent.duckdns.org` → localhost:8091 (qBittorrent)
- `tankesonarr.duckdns.org` → localhost:8989 (Sonarr)
- `tankejackett.duckdns.org` → localhost:9117 (Jackett)
- `tankeprowlarr.duckdns.org` → localhost:9696 (Prowlarr)
- `tankeradarr.duckdns.org` → localhost:7878 (Radarr)
- `tankejellyseerr.duckdns.org` → localhost:5055 (Jellyseerr)

### Volúmenes
- `caddy_data`: Certificados SSL y datos de Caddy
- `caddy_config`: Configuración interna de Caddy
- `C:\Cosas\Caddy\Logs`: Logs de Caddy (mantiene tu estructura actual)
- `./config`: Configuración DuckDNS (sin cambios)

## 🛠️ Comandos Útiles

### Gestión de Servicios
```bash
# Iniciar todos los servicios
docker compose up -d

# Detener todos los servicios
docker compose down

# Reiniciar solo Caddy (sin afectar DuckDNS)
docker compose restart caddy

# Reiniciar solo DuckDNS (sin afectar Caddy)
docker compose restart duckdns_jucago705
docker compose restart duckdns_jucago706
```

### Monitoreo
```bash
# Ver logs en tiempo real
docker compose logs -f

# Ver logs específicos de Caddy
docker compose logs -f caddy

# Ver logs específicos de DuckDNS
docker compose logs -f duckdns_jucago705

# Ver estado de servicios
docker compose ps
```

### Mantenimiento
```bash
# Actualizar imágenes
docker compose pull
docker compose up -d

# Validar configuración de Caddy
docker compose exec caddy caddy validate --config /etc/caddy/Caddyfile

# Recargar configuración de Caddy sin reiniciar
docker compose exec caddy caddy reload --config /etc/caddy/Caddyfile
```

## 🔍 Solución de Problemas

### Verificar Conectividad a Servicios del Host
```bash
# Probar desde el contenedor Caddy
docker compose exec caddy wget -qO- http://localhost:8096/health
```

### Verificar Certificados SSL
```bash
# Listar certificados gestionados por Caddy
docker compose exec caddy caddy list-certificates
```

### Problemas Comunes

1. **Servicios no accesibles desde Caddy**
   - Verifica que los servicios estén ejecutándose en el host
   - Confirma los puertos en el Caddyfile.host

2. **Certificados SSL no se generan**
   - Verifica que DuckDNS esté actualizando correctamente tu IP
   - Asegúrate de que los puertos 80 y 443 estén abiertos en tu router

3. **Logs no se escriben**
   - Verifica que el directorio `C:\Cosas\Caddy\Logs` exista
   - Confirma permisos de escritura en el directorio

## ⚠️ Notas Importantes

1. **No modifiques el docker-compose.yml original** - Esta versión mantiene tu configuración DuckDNS intacta
2. **Tus servicios deben seguir ejecutándose en el host** - Caddy actúa como proxy hacia ellos
3. **Los certificados SSL se generan automáticamente** - Let's Encrypt se encarga de esto
4. **Los logs mantienen tu estructura actual** - Se escriben en `C:\Cosas\Caddy\Logs` como antes

## 🔄 Rollback (Volver Atrás)

Si necesitas volver a tu configuración anterior:

1. Detén los servicios: `docker compose down`
2. Restaura el docker-compose.yml original (solo tenía DuckDNS)
3. Inicia solo DuckDNS: `docker compose up -d`

Tu configuración DuckDNS seguirá funcionando exactamente como antes.

## 📊 Beneficios de esta Integración

✅ **SSL automático** para todos tus dominios
✅ **Headers de seguridad** optimizados
✅ **Logs estructurados** con rotación automática
✅ **Configuración centralizada** de reverse proxy
✅ **Sin interrupciones** en tu setup actual
✅ **Fácil rollback** si es necesario
