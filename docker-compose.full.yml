# Docker Compose completo con todos los servicios containerizados
# Este archivo es opcional - úsalo si quieres dockerizar también tus aplicaciones

version: "3.8"

services:
  # === CADDY REVERSE PROXY ===
  caddy:
    image: caddy:2-alpine
    container_name: caddy_proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "443:443/udp"
    environment:
      - CADDY_INGRESS_NETWORKS=caddy_network
    volumes:
      - ./Caddyfile.containers:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - ./logs:/var/log/caddy
    networks:
      - caddy_network
    depends_on:
      - jellyfin
      - qbittorrent
      - sonarr
      - radarr
      - jackett
      - prowlarr
      - jellyseerr

  # === MEDIA SERVICES ===
  jellyfin:
    image: lscr.io/linuxserver/jellyfin:latest
    container_name: jellyfin
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
    volumes:
      - ./jellyfin/config:/config
      - ./jellyfin/cache:/cache
      - /path/to/media:/media:ro  # Ajustar ruta
    networks:
      - caddy_network
    # No exponemos puertos - solo acceso interno

  qbittorrent:
    image: lscr.io/linuxserver/qbittorrent:latest
    container_name: qbittorrent
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
      - WEBUI_PORT=8091
    volumes:
      - ./qbittorrent/config:/config
      - /path/to/downloads:/downloads  # Ajustar ruta
    networks:
      - caddy_network

  sonarr:
    image: lscr.io/linuxserver/sonarr:latest
    container_name: sonarr
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
    volumes:
      - ./sonarr/config:/config
      - /path/to/tv:/tv  # Ajustar ruta
      - /path/to/downloads:/downloads  # Ajustar ruta
    networks:
      - caddy_network

  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
    volumes:
      - ./radarr/config:/config
      - /path/to/movies:/movies  # Ajustar ruta
      - /path/to/downloads:/downloads  # Ajustar ruta
    networks:
      - caddy_network

  jackett:
    image: lscr.io/linuxserver/jackett:latest
    container_name: jackett
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
    volumes:
      - ./jackett/config:/config
      - /path/to/downloads:/downloads  # Ajustar ruta
    networks:
      - caddy_network

  prowlarr:
    image: lscr.io/linuxserver/prowlarr:latest
    container_name: prowlarr
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
    volumes:
      - ./prowlarr/config:/config
    networks:
      - caddy_network

  jellyseerr:
    image: fallenbagel/jellyseerr:latest
    container_name: jellyseerr
    restart: unless-stopped
    environment:
      - LOG_LEVEL=debug
      - TZ=${TZ:-Europe/Madrid}
    volumes:
      - ./jellyseerr/config:/app/config
    networks:
      - caddy_network

  # === DUCKDNS UPDATERS ===
  duckdns_jucago705:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_updater_jucago705
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
      - TOKEN=${DUCKDNS_TOKEN_1}
      - DOMAINS=${DUCKDNS_DOMAINS_1}
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./duckdns_updater/config:/config
    networks:
      - caddy_network

  duckdns_jucago706:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_updater_jucago706
    restart: unless-stopped
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Europe/Madrid}
      - TOKEN=${DUCKDNS_TOKEN_2}
      - DOMAINS=${DUCKDNS_DOMAINS_2}
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./duckdns_updater/config:/config
    networks:
      - caddy_network

volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local

networks:
  caddy_network:
    driver: bridge
    name: caddy_network
