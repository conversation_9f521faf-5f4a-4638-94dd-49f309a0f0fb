# Docker Compose COMPLETAMENTE AUTOMÁTICO
# Todo se inicia automáticamente al encender Docker
# Incluye: Caddy + DuckDNS + Todos los servicios del media server

version: "3.8"

services:
  # === CADDY REVERSE PROXY ===
  caddy:
    image: caddy:2-alpine
    container_name: caddy_proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "443:443/udp"
    volumes:
      - ../Caddyfile.containers:/etc/caddy/Caddyfile:ro
      - caddy_data:/data
      - caddy_config:/config
      - C:\Cosas\Caddy\Logs:/var/log/caddy
    networks:
      - media_network
    depends_on:
      - jellyfin
      - qbittorrent
      - sonarr
      - radarr
      - jackett
      - prowlarr
      - jellyseerr
      - duckdns_jucago705
      - duckdns_jucago706

  # === MEDIA SERVICES ===
  jellyfin:
    image: lscr.io/linuxserver/jellyfin:latest
    container_name: jellyfin
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - jellyfin_config:/config
      - jellyfin_cache:/cache
      # AJUSTA ESTAS RUTAS A TUS DIRECTORIOS REALES:
      - "D:\\Media\\Movies:/movies:ro"
      - "D:\\Media\\TV:/tv:ro"
      - "D:\\Media\\Music:/music:ro"
    networks:
      - media_network
    # GPU acceleration (opcional, descomenta si tienes GPU compatible)
    # devices:
    #   - /dev/dri:/dev/dri

  qbittorrent:
    image: lscr.io/linuxserver/qbittorrent:latest
    container_name: qbittorrent
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - WEBUI_PORT=8091
    volumes:
      - qbittorrent_config:/config
      # AJUSTA ESTA RUTA A TU DIRECTORIO DE DESCARGAS:
      - "D:\\Downloads:/downloads"
    networks:
      - media_network

  sonarr:
    image: lscr.io/linuxserver/sonarr:latest
    container_name: sonarr
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - sonarr_config:/config
      # AJUSTA ESTAS RUTAS:
      - "D:\\Media\\TV:/tv"
      - "D:\\Downloads:/downloads"
    networks:
      - media_network

  radarr:
    image: lscr.io/linuxserver/radarr:latest
    container_name: radarr
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - radarr_config:/config
      # AJUSTA ESTAS RUTAS:
      - "D:\\Media\\Movies:/movies"
      - "D:\\Downloads:/downloads"
    networks:
      - media_network

  jackett:
    image: lscr.io/linuxserver/jackett:latest
    container_name: jackett
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - jackett_config:/config
      - "D:\\Downloads:/downloads"
    networks:
      - media_network

  prowlarr:
    image: lscr.io/linuxserver/prowlarr:latest
    container_name: prowlarr
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
    volumes:
      - prowlarr_config:/config
    networks:
      - media_network

  jellyseerr:
    image: fallenbagel/jellyseerr:latest
    container_name: jellyseerr
    restart: unless-stopped
    environment:
      - LOG_LEVEL=info
      - TZ=Europe/Madrid
    volumes:
      - jellyseerr_config:/app/config
    networks:
      - media_network

  # === DUCKDNS SERVICES ===
  duckdns_jucago705:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_updater_jucago705
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=*************-4963-bd13-fc1199f2f5d9 # <--- ¡CAMBIA ESTO!
      - DOMAINS=tanketorrent.duckdns.org,tankeflix.duckdns.org,tankesonarr.duckdns.org,tankejackett.duckdns.org,tankeprowlarr.duckdns.org,tankejellyseerr.duckdns.org
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./config:/config
    networks:
      - media_network

  duckdns_jucago706:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_updater_jucago706
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=1b37bdd3-38a3-4e5f-8eea-57a3c7419c7d # <--- ¡CAMBIA ESTO!
      - DOMAINS=tankeradarr.duckdns.org
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./config:/config
    networks:
      - media_network

# === VOLÚMENES PERSISTENTES ===
volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local
  jellyfin_config:
    driver: local
  jellyfin_cache:
    driver: local
  qbittorrent_config:
    driver: local
  sonarr_config:
    driver: local
  radarr_config:
    driver: local
  jackett_config:
    driver: local
  prowlarr_config:
    driver: local
  jellyseerr_config:
    driver: local

# === REDES ===
networks:
  media_network:
    driver: bridge
    name: media_network
