{"Protocol":0,"Id":"48dd5c863741849b65f4f8979b46f756","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 4/<PERSON> and <PERSON><PERSON><PERSON> - S04E10 - Star Mort - Rickturn of the Jerri HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":271346749,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S04E10 - Star Mort - Rickturn of the Jerri HDTV-720p","IsRemote":false,"ETag":"0c718ea125fd96374f6cb875ddb2bcb7","RunTimeTicks":13140800000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1233315,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":108,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":113,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1651934,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E10 - Star Mort - Rickturn of the Jerri HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/099d9a72eed3fb3bec80405e641b5697%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/099d9a72eed3fb3bec80405e641b5697.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x55bf400a3340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E10 - Star Mort - Rickturn of the Jerri HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : Star Mort Rickturn of the Jerri
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Ser padres es una movida, bro. Cosas que deasaparecen en este episodio, bro.
    show            : Rick y Morty
    episode_id      : 10
    season_number   : 4
  Duration: 00:21:54.08, start: 0.000000, bitrate: 1651 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1233 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x55bf400fec00] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1233 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56970.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56971.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56972.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56973.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56974.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56975.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56976.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56977.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56978.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b56979.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569710.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569711.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569712.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569713.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569714.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569715.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569716.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569717.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569718.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569719.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569720.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569721.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569722.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569723.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569724.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569725.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569726.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569727.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569728.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569729.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569730.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569731.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569732.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569733.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569734.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569735.ts' for writing
size=N/A time=00:01:50.97 bitrate=N/A speed= 111x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569736.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569737.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569738.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569739.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569740.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569741.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569742.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569743.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569744.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569745.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569746.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569747.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569748.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569749.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569750.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569751.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569752.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569753.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569754.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569755.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569756.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569757.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569758.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569759.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569760.ts' for writing
size=N/A time=00:04:15.16 bitrate=N/A speed= 170x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569761.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569762.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569763.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569764.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569765.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569766.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569767.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569768.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569769.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569770.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569771.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569772.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569773.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569774.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569775.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569776.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569777.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569778.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569779.ts' for writing
size=N/A time=00:06:08.86 bitrate=N/A speed= 184x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569780.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569781.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569782.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569783.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569784.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569785.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569786.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569787.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569788.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569789.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569790.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569791.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569792.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569793.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569794.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569795.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569796.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569797.ts' for writing
size=N/A time=00:08:04.86 bitrate=N/A speed= 194x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569798.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b569799.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697100.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697101.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697102.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697103.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697104.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697105.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697106.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697107.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697108.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697109.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697110.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697111.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697112.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697113.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697114.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697115.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697116.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697117.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697118.ts' for writing
size=N/A time=00:10:05.85 bitrate=N/A speed= 202x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697119.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697120.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697121.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697122.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697123.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697124.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697125.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697126.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697127.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697128.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697129.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697130.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697131.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697132.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697133.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697134.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697135.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697136.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697137.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697138.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697139.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697140.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697141.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697142.ts' for writing
size=N/A time=00:12:29.05 bitrate=N/A speed= 214x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697143.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697144.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697145.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697146.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697147.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697148.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697149.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697150.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697151.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697152.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697153.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697154.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697155.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697156.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697157.ts' for writing
size=N/A time=00:14:00.86 bitrate=N/A speed= 210x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697158.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697159.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697160.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697161.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697162.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697163.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697164.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697165.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697166.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697167.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697168.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697169.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697170.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697171.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697172.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697173.ts' for writing
size=N/A time=00:15:34.68 bitrate=N/A speed= 208x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697174.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697175.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697176.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697177.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697178.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697179.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697180.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697181.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697182.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697183.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697184.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697185.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697186.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697187.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697188.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697189.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697190.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697191.ts' for writing
size=N/A time=00:17:27.48 bitrate=N/A speed= 209x    
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697192.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697193.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697194.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697195.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697196.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697197.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697198.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697199.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697200.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697201.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697202.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697203.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697204.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697205.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697206.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697207.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697208.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697209.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697210.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697211.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697212.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697213.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697214.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697215.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697216.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697217.ts' for writing
[hls @ 0x55bf400fd780] Opening '/cache/transcodes/099d9a72eed3fb3bec80405e641b5697218.ts' for writing
[out#0/hls @ 0x55bf400fec00] video:197821KiB audio:20532KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:20:01.07 bitrate=N/A speed= 219x    