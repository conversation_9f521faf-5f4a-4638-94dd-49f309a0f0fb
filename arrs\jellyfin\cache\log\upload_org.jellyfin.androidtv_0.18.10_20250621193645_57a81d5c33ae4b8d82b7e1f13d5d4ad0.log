---
client: <PERSON><PERSON><PERSON> for Android TV
client_version: 0.18.10
client_repository: https://github.com/jellyfin/jellyfin-androidtv
type: crash_report
format: markdown
---
### Logs

***Stack Trace***: 
```log
org.jellyfin.sdk.api.client.exception.TimeoutException: HTTP host unreachable
	at org.jellyfin.sdk.api.ktor.KtorClient.request(KtorClient.kt:151)
	at org.jellyfin.sdk.api.ktor.KtorClient$request$1.invokeSuspend(Unknown Source:19)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.EventLoop.processUnconfinedEvent(EventLoop.common.kt:65)
	at kotlinx.coroutines.internal.DispatchedContinuation.resumeWith(DispatchedContinuation.kt:343)
	at io.ktor.util.pipeline.SuspendFunctionGun.resumeRootWith(SuspendFunctionGun.kt:149)
	at io.ktor.util.pipeline.SuspendFunctionGun.access$resumeRootWith(SuspendFunctionGun.kt:11)
	at io.ktor.util.pipeline.SuspendFunctionGun$continuation$1.resumeWith(SuspendFunctionGun.kt:66)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at android.os.Handler.handleCallback(Handler.java:938)
	at android.os.Handler.dispatchMessage(Handler.java:99)
	at android.os.Looper.loop(Looper.java:223)
	at android.app.ActivityThread.main(ActivityThread.java:7719)
	at java.lang.reflect.Method.invoke(Native Method)
	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:947)
	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@6c63c08, Dispatchers.Main.immediate]
Caused by: java.net.UnknownHostException: Unable to resolve host "tankeflix.duckdns.org": No address associated with hostname
	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:923)

```  
***Logcat***: 
```log
06-21 21:40:44.186 W/AmazonKeyEventLogging(20645): remote_perf key event latency detected [Source: ViewRootImpl, KeyCode: KEYCODE_XXXX, IsDown: 1, Repeat: 0, EventTime: 1295396651, Latency: 345, DeviceId: 22, AppPackage: org.jellyfin.androidtv]
06-21 21:40:44.231 W/AmazonKeyEventLogging(20645): remote_perf key event latency detected [Source: ViewRootImpl, KeyCode: KEYCODE_XXXX, IsDown: 0, Repeat: 0, EventTime: 1295396810, Latency: 233, DeviceId: 22, AppPackage: org.jellyfin.androidtv]
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): Failed to report stopped playback!
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): java.util.concurrent.CancellationException: Job was cancelled
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at io.ktor.client.engine.UtilsKt$attachToUserJob$cleanupHandler$1.invoke(Utils.kt:98)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at io.ktor.client.engine.UtilsKt$attachToUserJob$cleanupHandler$1.invoke(Utils.kt:96)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.InvokeOnCancelling.invoke(JobSupport.kt:1571)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.notifyCancelling(JobSupport.kt:1604)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.tryMakeCancelling(JobSupport.kt:826)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.makeCancelling(JobSupport.kt:786)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.cancelImpl$kotlinx_coroutines_core(JobSupport.kt:702)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.parentCancelled(JobSupport.kt:668)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.ChildHandleNode.invoke(JobSupport.kt:1580)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.notifyCancelling(JobSupport.kt:1604)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.tryMakeCompletingSlowPath(JobSupport.kt:931)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.tryMakeCompleting(JobSupport.kt:894)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.cancelMakeCompleting(JobSupport.kt:727)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.cancelImpl$kotlinx_coroutines_core(JobSupport.kt:698)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.cancelInternal(JobSupport.kt:663)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobSupport.cancel(JobSupport.kt:648)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobKt__JobKt.cancel(Job.kt:548)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobKt.cancel(Unknown Source:1)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobKt__JobKt.cancel$default(Job.kt:547)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at kotlinx.coroutines.JobKt.cancel$default(Unknown Source:1)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.LifecycleCoroutineScopeImpl.onStateChanged(Lifecycle.kt:374)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.LifecycleRegistry$ObserverWithState.dispatchEvent(LifecycleRegistry.jvm.kt:320)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.LifecycleRegistry.backwardPass(LifecycleRegistry.jvm.kt:272)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.LifecycleRegistry.sync(LifecycleRegistry.jvm.kt:289)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.LifecycleRegistry.moveToState(LifecycleRegistry.jvm.kt:142)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.LifecycleRegistry.handleLifecycleEvent(LifecycleRegistry.jvm.kt:124)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.ReportFragment$Companion.dispatch$lifecycle_runtime_release(ReportFragment.android.kt:190)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at androidx.lifecycle.ReportFragment$LifecycleCallbacks.onActivityPreDestroyed(ReportFragment.android.kt:146)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.Activity.dispatchActivityPreDestroyed(Activity.java:1511)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.Activity.performDestroy(Activity.java:8284)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.Instrumentation.callActivityOnDestroy(Instrumentation.java:1344)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.ActivityThread.performDestroyActivity(ActivityThread.java:5142)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.ActivityThread.handleDestroyActivity(ActivityThread.java:5186)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.servertransaction.DestroyActivityItem.execute(DestroyActivityItem.java:44)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.servertransaction.TransactionExecutor.executeLifecycleState(TransactionExecutor.java:176)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:97)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2081)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.os.Handler.dispatchMessage(Handler.java:106)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.os.Looper.loop(Looper.java:223)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at android.app.ActivityThread.main(ActivityThread.java:7719)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at java.lang.reflect.Method.invoke(Native Method)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
06-21 21:40:44.549 E/ReportingHelper$reportStopped(20645): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:947)
06-21 21:40:44.972 I/org.jellyfin.sdk.api.ktor.KtorClient(20645): GET https://tankeflix.duckdns.org/Items/0b6fd06e-089b-b8c1-f230-df35550c9ac8
06-21 21:40:46.908 I/lyfin.androidt(20645): Background concurrent copying GC freed 80715(2735KB) AllocSpace objects, 0(0B) LOS objects, 49% free, 7314KB/14MB, paused 164us total 245.854ms
06-21 21:40:46.940 I/lyfin.androidt(20645): Could not find corresponding native window for surface
06-21 21:40:46.962 I/SessionRepositoryImpl$restoreSession(20645): Restoring session
06-21 21:40:49.844 I/org.jellyfin.sdk.api.ktor.KtorClient(20645): GET https://tankeflix.duckdns.org/Items/0b6fd06e-089b-b8c1-f230-df35550c9ac8
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): Failed to get item 0b6fd06e-089b-b8c1-f230-df35550c9ac8
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): org.jellyfin.sdk.api.client.exception.TimeoutException: HTTP host unreachable
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at org.jellyfin.sdk.api.ktor.KtorClient.request(KtorClient.kt:151)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at org.jellyfin.sdk.api.ktor.KtorClient$request$1.invokeSuspend(Unknown Source:19)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at kotlinx.coroutines.EventLoop.processUnconfinedEvent(EventLoop.common.kt:65)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at kotlinx.coroutines.internal.DispatchedContinuation.resumeWith(DispatchedContinuation.kt:343)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at io.ktor.util.pipeline.SuspendFunctionGun.resumeRootWith(SuspendFunctionGun.kt:149)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at io.ktor.util.pipeline.SuspendFunctionGun.access$resumeRootWith(SuspendFunctionGun.kt:11)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at io.ktor.util.pipeline.SuspendFunctionGun$continuation$1.resumeWith(SuspendFunctionGun.kt:66)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at android.os.Handler.handleCallback(Handler.java:938)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at android.os.Handler.dispatchMessage(Handler.java:99)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at android.os.Looper.loop(Looper.java:223)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at android.app.ActivityThread.main(ActivityThread.java:7719)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.lang.reflect.Method.invoke(Native Method)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:592)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:947)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): Caused by: java.net.UnknownHostException: Unable to resolve host "tankeflix.duckdns.org": No address associated with hostname
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
06-21 21:40:49.851 W/FullDetailsFragmentHelperKt$getItem(20645): 	at java.lang.Thread.run(Thread.java:923)
06-21 21:40:50.881 I/org.jellyfin.sdk.api.ktor.KtorClient(20645): POST https://tankeflix.duckdns.org/UserPlayedItems/0b6fd06e-089b-b8c1-f230-df35550c9ac8
06-21 21:40:50.905 E/ACRA    (20645): ACRA caught a TimeoutException for org.jellyfin.androidtv

```  

### App information

***App version***: `0.18.10` (`181099`)  
***Package name***: `org.jellyfin.androidtv`  
***Build***: 
```json
{"BOARD":"karat","BOOTLOADER":"unknown","BRAND":"Amazon","CPU_ABI":"armeabi-v7a","CPU_ABI2":"armeabi","DEVICE":"karat","DISPLAY":"RS8136.3332N","FINGERPRINT":"Amazon\/karat\/karat:11\/RS8136.3332N\/0027314160772:user\/amz-p,release-keys","HARDWARE":"mt8696","HOST":"c5-ri-1804-use1a-b-18-04-fos8-19","ID":"RS8136.3332N","IS_DEBUGGABLE":false,"IS_EMULATOR":false,"MANUFACTURER":"Amazon","MODEL":"AFTKRT","PERMISSIONS_REVIEW_REQUIRED":true,"PRODUCT":"karat","RADIO":"unknown","SUPPORTED_32_BIT_ABIS":["armeabi-v7a","armeabi"],"SUPPORTED_64_BIT_ABIS":[],"SUPPORTED_ABIS":["armeabi-v7a","armeabi"],"TAGS":"amz-p,release-keys","TIME":1746580738000,"TYPE":"user","UNKNOWN":"unknown","USER":"build","VERSION":{"ACTIVE_CODENAMES":[],"BASE_OS":"RP1A.200720.009","CODENAME":"REL","INCREMENTAL":"0027314160772","PREVIEW_SDK_FINGERPRINT":"REL","PREVIEW_SDK_INT":0,"RELEASE":"11","RELEASE_OR_CODENAME":"11","SDK":"30","SDK_INT":30,"SECURITY_PATCH":"2025-01-01"}}
```  
***Build config***: 
```json
{"APPLICATION_ID":"org.jellyfin.androidtv","BUILD_TYPE":"release","DEBUG":false,"DEVELOPMENT":false,"VERSION_CODE":181099,"VERSION_NAME":"0.18.10"}
```  

### Device information

***Android version***: `11`  
***Device brand***: `Amazon`  
***Device product***: `karat`  
***Device model***: `AFTKRT`  

### Crash information

***Start time***: `2025-06-21T18:21:50.941+02:00`  
***Crash time***: `2025-06-21T21:40:50.965+02:00`  

