{"Protocol":0,"Id":"5c96572b763589b6f71d35e252090b7c","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 2/<PERSON> and <PERSON><PERSON><PERSON> - S02E10 - The Wedding Squanchers HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":289617182,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S02E10 - The Wedding Squanchers HDTV-720p","IsRemote":false,"ETag":"811a9bd50c927cf31a1f6fd95cacf1c5","RunTimeTicks":13290880000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1281959,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":99,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":104,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1743253,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E10 - The Wedding Squanchers HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/581f7dad8af84504f254a00eefe8d30b%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/581f7dad8af84504f254a00eefe8d30b.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x55c558db7340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E10 - The Wedding Squanchers HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : De squanch en squanch
    date            : 2024
    encoder         : Lavf61.9.106
    description     : La familia entera es invitada a una boda en el lejano planeta Squanch.
    show            : Rick y Morty
    episode_id      : 10
    season_number   : 2
  Duration: 00:22:09.09, start: 0.000000, bitrate: 1743 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1281 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x55c558dcfa00] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1281 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b0.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b1.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b2.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b3.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b4.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b5.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b6.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b7.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b8.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b9.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b10.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b11.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b12.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b13.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b14.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b15.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b16.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b17.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b18.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b19.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b20.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b21.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b22.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b23.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b24.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b25.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b26.ts' for writing
size=N/A time=00:01:12.73 bitrate=N/A speed=72.7x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b27.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b28.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b29.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b30.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b31.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b32.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b33.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b34.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b35.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b36.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b37.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b38.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b39.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b40.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b41.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b42.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b43.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b44.ts' for writing
size=N/A time=00:02:59.80 bitrate=N/A speed= 120x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b45.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b46.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b47.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b48.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b49.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b50.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b51.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b52.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b53.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b54.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b55.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b56.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b57.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b58.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b59.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b60.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b61.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b62.ts' for writing
size=N/A time=00:04:47.80 bitrate=N/A speed= 144x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b63.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b64.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b65.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b66.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b67.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b68.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b69.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b70.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b71.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b72.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b73.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b74.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b75.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b76.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b77.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b78.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b79.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b80.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b81.ts' for writing
size=N/A time=00:06:39.80 bitrate=N/A speed= 160x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b82.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b83.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b84.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b85.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b86.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b87.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b88.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b89.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b90.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b91.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b92.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b93.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b94.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b95.ts' for writing
size=N/A time=00:08:07.45 bitrate=N/A speed= 162x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b96.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b97.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b98.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b99.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b100.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b101.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b102.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b103.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b104.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b105.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b106.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b107.ts' for writing
size=N/A time=00:09:24.80 bitrate=N/A speed= 161x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b108.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b109.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b110.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b111.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b112.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b113.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b114.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b115.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b116.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b117.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b118.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b119.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b120.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b121.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b122.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b123.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b124.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b125.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b126.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b127.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b128.ts' for writing
size=N/A time=00:11:28.73 bitrate=N/A speed= 172x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b129.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b130.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b131.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b132.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b133.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b134.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b135.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b136.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b137.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b138.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b139.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b140.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b141.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b142.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b143.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b144.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b145.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b146.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b147.ts' for writing
size=N/A time=00:13:19.90 bitrate=N/A speed= 178x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b148.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b149.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b150.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b151.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b152.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b153.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b154.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b155.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b156.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b157.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b158.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b159.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b160.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b161.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b162.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b163.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b164.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b165.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b166.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b167.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b168.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b169.ts' for writing
size=N/A time=00:15:32.41 bitrate=N/A speed= 186x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b170.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b171.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b172.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b173.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b174.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b175.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b176.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b177.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b178.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b179.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b180.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b181.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b182.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b183.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b184.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b185.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b186.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b187.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b188.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b189.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b190.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b191.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b192.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b193.ts' for writing
size=N/A time=00:18:00.80 bitrate=N/A speed= 196x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b194.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b195.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b196.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b197.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b198.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b199.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b200.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b201.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b202.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b203.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b204.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b205.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b206.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b207.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b208.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b209.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b210.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b211.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b212.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b213.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b214.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b215.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b216.ts' for writing
size=N/A time=00:20:12.99 bitrate=N/A speed= 202x    
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b217.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b218.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b219.ts' for writing
[hls @ 0x55c558dd03c0] Opening '/cache/transcodes/581f7dad8af84504f254a00eefe8d30b220.ts' for writing
[out#0/hls @ 0x55c558dcfa00] video:207971KiB audio:20767KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:20:36.71 bitrate=N/A speed= 204x    