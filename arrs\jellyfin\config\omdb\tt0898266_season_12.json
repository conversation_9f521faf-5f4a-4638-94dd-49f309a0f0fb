{"Title": "The Big Bang Theory", "seriesID": "tt0898266", "Season": 12, "totalSeasons": 12, "Episodes": [{"Title": "The Conjugal Configuration", "Year": "2018", "Rated": "TV-14", "Released": "24 Sep 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "After their wedding, <PERSON> and <PERSON> go on their honeymoon.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNjFhMDkzNzktNWJiOC00NGE2LWI2ODYtYjRkNGVkZjM1MDkwXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "3025", "imdbID": "tt6673778", "Episode": 1}, {"Title": "The Wedding Gift Wormhole", "Year": "2018", "Rated": "TV-PG", "Released": "27 Sep 2018", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> give <PERSON> and <PERSON> a baffling wedding gift.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BOWNiY2QyN2EtODU4MS00OWVkLWEzMTItYzgxZDU3YTkyMTdjXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.6", "imdbVotes": "2803", "imdbID": "tt6674688", "Episode": 2}, {"Title": "The Procreation Calculation", "Year": "2018", "Rated": "TV-PG", "Released": "04 Oct 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "The <PERSON><PERSON><PERSON><PERSON>' life gets complicated when <PERSON> starts bringing his new girlfriend home. Also, <PERSON> and <PERSON> talk about starting a family while <PERSON><PERSON><PERSON><PERSON><PERSON> explores an arranged marriage.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYjRlOWJiZjctNzBjYS00ZWZlLWFjNWQtMzMwMmE3Y2U5Mjg0XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2663", "imdbID": "tt6674692", "Episode": 3}, {"Title": "The Tam Turbulence", "Year": "2018", "Rated": "TV-PG", "Released": "11 Oct 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> won't tell anyone why he's never mentioned <PERSON>, his childhood best friend, so <PERSON> takes matters into his own hands. Also, <PERSON><PERSON><PERSON> and <PERSON> take out <PERSON>'s fiancée, <PERSON><PERSON>, for dinner to get the skinny on her.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMzIzY2JlOWYtNmY4MC00OGZlLTgwNzItY2EwMGJkMjFjOTBlXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.0", "imdbVotes": "2573", "imdbID": "tt6674690", "Episode": 4}, {"Title": "The Planetarium Collision", "Year": "2018", "Rated": "TV-PG", "Released": "18 Oct 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> doesn't have time to work with <PERSON> on super asymmetry, so he goes to great lengths to ensure she's available. <PERSON><PERSON><PERSON><PERSON><PERSON> doesn't want <PERSON><PERSON><PERSON> to join him in hosting a show at the planetarium.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNmUzZTMzNTgtZGQ4Yi00YjAwLWE0MzYtNDQ5MjExNTYwZDBhXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "6.8", "imdbVotes": "2591", "imdbID": "tt6674694", "Episode": 5}, {"Title": "The Imitation Perturbation", "Year": "2018", "Rated": "TV-PG", "Released": "25 Oct 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "When <PERSON><PERSON><PERSON> dresses up as <PERSON> for Halloween, <PERSON> seeks retaliation at <PERSON> and <PERSON>'s Halloween party. Also, <PERSON> is shocked that <PERSON> doesn't remember their first kiss.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYmEzMjg0NDItMzU2YS00NTAxLTg4YjQtOWRlNDM2YTNiZDkzXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "8.2", "imdbVotes": "3156", "imdbID": "tt6674704", "Episode": 6}, {"Title": "The Grant Allocation Derivation", "Year": "2018", "Rated": "TV-PG", "Released": "01 Nov 2018", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> is caught between a rock and a hard place when he's responsible for distributing extra grant money. Also, <PERSON><PERSON><PERSON> turns the backyard playhouse into a hideaway from her husband and kids.", "Language": "English, German", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMGFlYzIxNmMtNjgyMi00YjNkLTg0MDEtN2NkZmE4MjllNGIzXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.3", "imdbVotes": "2656", "imdbID": "tt6674696", "Episode": 7}, {"Title": "The Consummation Deviation", "Year": "2018", "Rated": "TV-PG", "Released": "08 Nov 2018", "Runtime": "22 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> tries to bond with <PERSON>'s father, but when <PERSON><PERSON><PERSON> lures Mr<PERSON> away with magic tricks, <PERSON> has no choice but to bond with Mrs. <PERSON>. Also, <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON> try to get physical for the first time.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMmNmMTU5YmItYjJiNC00ZDUwLTkwYTEtMDhmMzU1YWRkOTI0XkEyXkFqcGdeQXVyMTYzMTY1MjQ@._V1_SX300.jpg", "imdbRating": "7.3", "imdbVotes": "2552", "imdbID": "tt6674702", "Episode": 8}, {"Title": "The Citation Negation", "Year": "2018", "Rated": "TV-PG", "Released": "15 Nov 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON><PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> are devastated after learning from a Russian paper that super asymmetry has already been theorized and disproved. <PERSON><PERSON><PERSON> wants to beat <PERSON> in a popular video game.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BM2VlNjQ4MGEtZDM5NC00ZDlmLThmOWUtMzBjY2YzZDYyZWUxXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2544", "imdbID": "tt6674698", "Episode": 9}, {"Title": "The VCR Illumination", "Year": "2018", "Rated": "TV-14", "Released": "06 Dec 2018", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "A VHS tape from <PERSON>'s past inspires him and <PERSON> not to give up when they're still feeling down about their theory being disproven. <PERSON><PERSON><PERSON> turns into a pageant mom when she tries to help <PERSON> with his Magic Castle audition.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BODVjOTQ3NDItMjE1Mi00ZjhmLWEzOGItYzZlNTE3NjNiMWI1XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.4", "imdbVotes": "2670", "imdbID": "tt6674710", "Episode": 10}, {"Title": "The Paintball Scattering", "Year": "2019", "Rated": "TV-PG", "Released": "03 Jan 2019", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> organize a paintball game, which results in mayhem when <PERSON> becomes jealous of <PERSON>. <PERSON> catches <PERSON><PERSON> with her ex-boyfriend. <PERSON> doesn't want to move in with <PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMDZmYjljNjUtNWNkNi00ZTUzLThkNDYtYmMwYTJmNjBhODg1XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.3", "imdbVotes": "2513", "imdbID": "tt6674706", "Episode": 11}, {"Title": "The Propagation Proposition", "Year": "2019", "Rated": "TV-PG", "Released": "10 Jan 2019", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON>'s ex-boyfriend <PERSON> learns that he is infertile, so he asks <PERSON> for help; <PERSON><PERSON><PERSON><PERSON><PERSON> attempts to mend things with his ex-fiancee <PERSON><PERSON>.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BZGY2ZDI0ODktNDFkNy00ZWVkLTk0ZDMtNjJhZmU2MzExZDRjXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2458", "imdbID": "tt6674708", "Episode": 12}, {"Title": "The Confirmation Polarization", "Year": "2019", "Rated": "TV-PG", "Released": "17 Jan 2019", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> are thrilled when their super asymmetry theory is proven by two physicists, until they try to kick <PERSON> off the Nobel nomination. <PERSON><PERSON><PERSON> gets one of her drugs approved and wants a reluctant <PERSON> to head the sal...", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BN2Y2NTRkOGEtODBlZC00M2Q4LWI5NGItMzNhMDczNzNhNDU3XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2588", "imdbID": "tt6674714", "Episode": 13}, {"Title": "The Meteorite Manifestation", "Year": "2019", "Rated": "TV-14", "Released": "31 Jan 2019", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> is thrilled to help <PERSON><PERSON><PERSON> and <PERSON> navigate bureaucracy concerning a neighbor's non-compliant balcony until he learns they too have ignored codes. <PERSON> is disappointed when his friends are uninterested in his new...", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNjI1NmMyNGYtYmRiZS00Y2VhLWE3NGItZmJjNjkzNjE2MmEzXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2487", "imdbID": "tt6674712", "Episode": 14}, {"Title": "The Donation Oscillation", "Year": "2019", "Rated": "TV-PG", "Released": "07 Feb 2019", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> tries to throw a wrench into <PERSON>'s plan to be a sperm donor for her ex-boyfriend; <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON> turn <PERSON><PERSON><PERSON><PERSON><PERSON>'s canceled bachelor party into a couple's trip aboard the \"vomit comet.\"", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMGE0NWY3YTgtNjdkNy00YjkzLThjYzAtNjBhNDg1ODMwMmEwXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.4", "imdbVotes": "2538", "imdbID": "tt6674716", "Episode": 15}, {"Title": "The D & D Vortex", "Year": "2019", "Rated": "TV-PG", "Released": "21 Feb 2019", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "The gang finds out <PERSON><PERSON> hosts a celebrity Dungeons and Dragons game involving <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>. Deception and betrayal are the path to make it to the one open seat.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNjZjMWZjNGMtOTg1NC00ZjViLWIxM2YtYmMwYWNlMDczOTVhXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.9", "imdbVotes": "2803", "imdbID": "tt6674718", "Episode": 16}, {"Title": "The Conference Valuation", "Year": "2019", "Rated": "TV-PG", "Released": "07 Mar 2019", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON><PERSON><PERSON> and <PERSON> travel to San Diego for a pharmaceutical sales convention. Back home, <PERSON> and the guys experiment on the <PERSON><PERSON><PERSON> children when <PERSON> finds a book on it.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BY2FmYzQxMzEtOTkxNS00NWZhLWE5NTgtMDIyOTlkOThlMjRjXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2555", "imdbID": "tt6674726", "Episode": 17}, {"Title": "The Laureate Accumulation", "Year": "2019", "Rated": "TV-PG", "Released": "04 Apr 2019", "Runtime": "21 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "Rivals <PERSON><PERSON><PERSON><PERSON> and <PERSON> charm America on a publicity tour so <PERSON> and <PERSON> try to win over Nobel laureates <PERSON><PERSON>, <PERSON> and <PERSON>. <PERSON><PERSON>'s fear of the dark causes opportunity and conflict at the Wolow...", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMzI2ZjkzOWUtNzAyYi00YzM2LTk0ZDItNDg0ZTRhNTkzNjQ1XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2420", "imdbID": "tt6674724", "Episode": 18}, {"Title": "The Inspiration Deprivation", "Year": "2019", "Rated": "TV-PG", "Released": "18 Apr 2019", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "Contemplating what it would mean to all women if she were to win a Nobel Prize causes <PERSON> to have a meltdown. <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> try to relive the good old days after <PERSON><PERSON><PERSON> buys a scooter that looks like the one he had y...", "Language": "English, Swedish", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BOGI1MWRjMzAtNjNiZS00ZmE1LTgxMGYtMDdiZWVkMzIzYzZkXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.0", "imdbVotes": "2487", "imdbID": "tt6674720", "Episode": 19}, {"Title": "The Decision Reverberation", "Year": "2019", "Rated": "TV-PG", "Released": "25 Apr 2019", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON><PERSON><PERSON><PERSON><PERSON> is worried people won't take him seriously in his own field after publishing a paper that suggests he may have discovered alien life. <PERSON> wants to be the principal investigator on a plasma physics study.", "Language": "English, Hawaiian, Russian", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BM2Q3OWM2OWYtYzE5Ny00OWE5LWEzNjktYzg2YWFhZDg1ZThkXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.1", "imdbVotes": "2478", "imdbID": "tt6674728", "Episode": 20}, {"Title": "The Plagiarism Schism", "Year": "2019", "Rated": "TV-PG", "Released": "02 May 2019", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON><PERSON><PERSON> has proof that <PERSON><PERSON> plagiarized his thesis in college, and <PERSON> and <PERSON> aren't sure if they should turn him in. <PERSON><PERSON><PERSON> learns that <PERSON><PERSON><PERSON> wasn't the only waitress at the Cheesecake Factory who had a crush ...", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMzkxZjZiNDItMTY2Yi00MTgwLWJkMWUtYjQyYzUwYWE4NDUxXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.2", "imdbVotes": "2517", "imdbID": "tt6674732", "Episode": 21}, {"Title": "The Maternal Conclusion", "Year": "2019", "Rated": "TV-PG", "Released": "09 May 2019", "Runtime": "20 min", "Genre": "Comedy, Romance", "Director": "<PERSON><PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> is pleasantly surprised when <PERSON> comes to visit and she's genuinely kind to him, until he finds out the real reason she's there. Also, <PERSON> and <PERSON> overstay their welcome at the Wolowitz house.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BYjE2YTQxOTEtNzM3OC00ZDlkLWJmMWItYjM3YmVkYTE5MTQxXkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "7.9", "imdbVotes": "3054", "imdbID": "tt6674730", "Episode": 22}, {"Title": "The Change Constant", "Year": "2019", "Rated": "TV-PG", "Released": "16 May 2019", "Runtime": "19 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON> and <PERSON> await big news.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BNmQ0MDY3OTUtMTIzMi00M2I4LTkwNzAtMjNkZjdiNTExMTk5XkEyXkFqcGc@._V1_SX300.jpg", "imdbVotes": "5329", "imdbID": "tt6674734", "Episode": 23}, {"Title": "The Stockholm Syndrome", "Year": "2019", "Rated": "TV-PG", "Released": "16 May 2019", "Runtime": "23 min", "Genre": "Comedy, Romance", "Director": "<PERSON>", "Writer": "<PERSON>, <PERSON>, <PERSON>", "Actors": "<PERSON>, <PERSON>, <PERSON><PERSON>", "Plot": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> leave their kids for the first time; <PERSON> and <PERSON> try to keep a secret; <PERSON> and <PERSON> stick together; and <PERSON><PERSON><PERSON><PERSON><PERSON> makes a new friend, as the gang travels together into an uncharted future.", "Language": "English", "Country": "United States", "Poster": "https://m.media-amazon.com/images/M/MV5BMGUyYzc5YmItOThhZC00ZDMzLTllZDYtNjY0MDIwNmY1Zjk4XkEyXkFqcGc@._V1_SX300.jpg", "imdbRating": "9.5", "imdbVotes": "11055", "imdbID": "tt6674736", "Episode": 24}], "Response": "True"}