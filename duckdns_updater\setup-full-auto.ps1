# Script para configuración COMPLETAMENTE AUTOMÁTICA
# Todo se inicia automáticamente al encender Docker

Write-Host "=== CONFIGURACIÓN COMPLETAMENTE AUTOMÁTICA ===" -ForegroundColor Green
Write-Host "Este script configurará TODO para que se inicie automáticamente" -ForegroundColor Yellow
Write-Host ""

# Verificar Docker
Write-Host "Verificando Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker encontrado: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker no está instalado" -ForegroundColor Red
    exit 1
}

# Configurar Docker para inicio automático
Write-Host "`nConfigurando Docker para inicio automático..." -ForegroundColor Yellow
try {
    # Configurar Docker Desktop para iniciar con Windows
    $dockerDesktopPath = "${env:ProgramFiles}\Docker\Docker\Docker Desktop.exe"
    if (Test-Path $dockerDesktopPath) {
        Write-Host "✓ Docker Desktop encontrado" -ForegroundColor Green
        Write-Host "Asegúrate de que Docker Desktop esté configurado para iniciar con Windows" -ForegroundColor Yellow
        Write-Host "Ve a Docker Desktop > Settings > General > Start Docker Desktop when you log in" -ForegroundColor White
    }
} catch {
    Write-Host "⚠ Configura Docker Desktop manualmente para iniciar con Windows" -ForegroundColor Yellow
}

# Preguntar por las rutas de medios
Write-Host "`n=== CONFIGURACIÓN DE RUTAS DE MEDIOS ===" -ForegroundColor Cyan
Write-Host "Necesito conocer las rutas de tus archivos de medios para configurar los volúmenes:" -ForegroundColor White

$mediaConfig = @{}

Write-Host "`n1. Ruta de películas (ej: D:\Media\Movies): " -ForegroundColor Yellow -NoNewline
$mediaConfig.Movies = Read-Host

Write-Host "2. Ruta de series (ej: D:\Media\TV): " -ForegroundColor Yellow -NoNewline
$mediaConfig.TV = Read-Host

Write-Host "3. Ruta de música (ej: D:\Media\Music): " -ForegroundColor Yellow -NoNewline
$mediaConfig.Music = Read-Host

Write-Host "4. Ruta de descargas (ej: D:\Downloads): " -ForegroundColor Yellow -NoNewline
$mediaConfig.Downloads = Read-Host

# Verificar rutas
Write-Host "`nVerificando rutas..." -ForegroundColor Yellow
foreach ($key in $mediaConfig.Keys) {
    $path = $mediaConfig[$key]
    if ($path -and (Test-Path $path)) {
        Write-Host "✓ $key : $path" -ForegroundColor Green
    } elseif ($path) {
        Write-Host "⚠ $key : $path (no existe, se creará)" -ForegroundColor Yellow
        try {
            New-Item -ItemType Directory -Path $path -Force | Out-Null
            Write-Host "✓ Directorio creado: $path" -ForegroundColor Green
        } catch {
            Write-Host "✗ No se pudo crear: $path" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ $key : No especificado" -ForegroundColor Yellow
    }
}

# Actualizar docker-compose con las rutas
Write-Host "`nActualizando configuración con tus rutas..." -ForegroundColor Yellow

$composeFile = "docker-compose.full-auto.yml"
if (Test-Path $composeFile) {
    $content = Get-Content $composeFile -Raw
    
    # Reemplazar rutas de ejemplo con las rutas reales
    if ($mediaConfig.Movies) {
        $content = $content -replace 'D:\\Media\\Movies', $mediaConfig.Movies.Replace('\', '\\')
    }
    if ($mediaConfig.TV) {
        $content = $content -replace 'D:\\Media\\TV', $mediaConfig.TV.Replace('\', '\\')
    }
    if ($mediaConfig.Music) {
        $content = $content -replace 'D:\\Media\\Music', $mediaConfig.Music.Replace('\', '\\')
    }
    if ($mediaConfig.Downloads) {
        $content = $content -replace 'D:\\Downloads', $mediaConfig.Downloads.Replace('\', '\\')
    }
    
    # Guardar archivo actualizado
    $content | Set-Content $composeFile -Encoding UTF8
    Write-Host "✓ Configuración actualizada con tus rutas" -ForegroundColor Green
} else {
    Write-Host "✗ No se encontró $composeFile" -ForegroundColor Red
    exit 1
}

# Crear directorios de logs
Write-Host "`nCreando directorios de logs..." -ForegroundColor Yellow
$logDir = "C:\Cosas\Caddy\Logs"
$logSubdirs = @("Jellyfin", "Qbittorrent", "Sonarr", "Jackett", "Prowlarr", "Radarr", "Jellyseerr")

if (!(Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force -Recurse | Out-Null
}

foreach ($subdir in $logSubdirs) {
    $fullPath = Join-Path $logDir $subdir
    if (!(Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "✓ Creado: $subdir" -ForegroundColor Green
    }
}

Write-Host "`n=== CONFIGURACIÓN COMPLETADA ===" -ForegroundColor Green
Write-Host ""
Write-Host "CONFIGURACIÓN COMPLETAMENTE AUTOMÁTICA:" -ForegroundColor Cyan
Write-Host "✓ Docker configurado para inicio automático" -ForegroundColor Green
Write-Host "✓ Todos los servicios configurados con restart: unless-stopped" -ForegroundColor Green
Write-Host "✓ Rutas de medios configuradas" -ForegroundColor Green
Write-Host "✓ Directorios de logs creados" -ForegroundColor Green
Write-Host ""
Write-Host "PRÓXIMOS PASOS:" -ForegroundColor Yellow
Write-Host "1. Ejecuta: docker compose -f docker-compose.full-auto.yml up -d" -ForegroundColor White
Write-Host "2. Espera a que todos los servicios se inicien (puede tardar unos minutos)" -ForegroundColor White
Write-Host "3. Configura cada servicio desde su interfaz web" -ForegroundColor White
Write-Host "4. ¡Listo! Todo se iniciará automáticamente al encender el PC" -ForegroundColor White
Write-Host ""
Write-Host "SERVICIOS DISPONIBLES:" -ForegroundColor Cyan
Write-Host "- Jellyfin: https://tankeflix.duckdns.org" -ForegroundColor White
Write-Host "- qBittorrent: https://tanketorrent.duckdns.org" -ForegroundColor White
Write-Host "- Sonarr: https://tankesonarr.duckdns.org" -ForegroundColor White
Write-Host "- Radarr: https://tankeradarr.duckdns.org" -ForegroundColor White
Write-Host "- Jackett: https://tankejackett.duckdns.org" -ForegroundColor White
Write-Host "- Prowlarr: https://tankeprowlarr.duckdns.org" -ForegroundColor White
Write-Host "- Jellyseerr: https://tankejellyseerr.duckdns.org" -ForegroundColor White
Write-Host ""
Write-Host "COMANDOS ÚTILES:" -ForegroundColor Yellow
Write-Host "- Ver estado: docker compose -f docker-compose.full-auto.yml ps" -ForegroundColor White
Write-Host "- Ver logs: docker compose -f docker-compose.full-auto.yml logs -f" -ForegroundColor White
Write-Host "- Detener todo: docker compose -f docker-compose.full-auto.yml down" -ForegroundColor White
