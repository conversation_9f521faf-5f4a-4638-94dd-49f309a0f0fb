{"Protocol":0,"Id":"541dca9240a14c13659eab8f5d811858","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 4/<PERSON> and <PERSON><PERSON><PERSON> - S04E07 - Promortyus HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":311755325,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S04E07 - Promortyus HDTV-720p","IsRemote":false,"ETag":"72d9878596e26659d0e1bc8f53d242ed","RunTimeTicks":12231040000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1612415,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":107,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":113,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":2039109,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -ss 00:08:06.500 -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E07 - Promortyus HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 81 -hls_segment_filename "/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x557ccccd9340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E07 - Promortyus HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : ProMortyus
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Pírate de aquí, bro.
    show            : Rick y Morty
    episode_id      : 7
    season_number   : 4
  Duration: 00:20:23.10, start: 0.000000, bitrate: 2039 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1612 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x557cccd00900] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1612 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8681.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8682.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8683.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8684.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8685.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8686.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8687.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8688.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8689.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8690.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8691.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8692.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8693.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8694.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8695.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8696.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8697.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8698.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd8699.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86100.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86101.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86102.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86103.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86104.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86105.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86106.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86107.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86108.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86109.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86110.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86111.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86112.ts' for writing
size=N/A time=00:01:57.82 bitrate=N/A speed= 118x    
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86113.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86114.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86115.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86116.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86117.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86118.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86119.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86120.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86121.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86122.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86123.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86124.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86125.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86126.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86127.ts' for writing
size=N/A time=00:03:29.82 bitrate=N/A speed= 140x    
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86128.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86129.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86130.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86131.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86132.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86133.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86134.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86135.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86136.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86137.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86138.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86139.ts' for writing
size=N/A time=00:04:41.82 bitrate=N/A speed= 141x    
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86140.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86141.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86142.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86143.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86144.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86145.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86146.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86147.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86148.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86149.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86150.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86151.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86152.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86153.ts' for writing
size=N/A time=00:06:08.16 bitrate=N/A speed= 147x    
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86154.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86155.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86156.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86157.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86158.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86159.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86160.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86161.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86162.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86163.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86164.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86165.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86166.ts' for writing
[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86167.ts' for writing


[q] command received. Exiting.

[hls @ 0x557ccccff5c0] Opening '/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86168.ts' for writing
[out#0/hls @ 0x557cccd00900] video:102570KiB audio:8260KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:07:32.67 bitrate=N/A speed= 150x    