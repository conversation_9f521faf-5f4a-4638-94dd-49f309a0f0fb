[2025-06-23 01:59:56.527 +02:00] [INF] [43] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-06-23 01:59:56.633 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-06-23 01:59:57.468 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-23 02:00:00.000 +02:00, which is 00:00:02.5317833 from now.
[2025-06-23 02:00:00.130 +02:00] [INF] [3] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-06-23 02:00:00.215 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-06-23 02:00:01.012 +02:00] [INF] [43] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-24 02:00:00.000 +02:00, which is 23:59:58.9878533 from now.
[2025-06-23 02:59:56.366 +02:00] [INF] [140] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-06-23 02:59:57.179 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-23 03:00:00.000 +02:00, which is 00:00:02.8205149 from now.
[2025-06-23 03:00:00.028 +02:00] [INF] [94] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-06-23 03:00:01.000 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-24 03:00:00.000 +02:00, which is 23:59:58.9997364 from now.
[2025-06-23 08:30:02.729 +02:00] [INF] [82] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-23 08:30:02.733 +02:00] [INF] [82] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-06-23 08:30:02.735 +02:00] [INF] [82] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-06-23 08:30:02.745 +02:00] [INF] [82] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-06-23 08:30:03.351 +02:00] [WRN] [82] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-23 08:30:04.119 +02:00] [WRN] [72] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-23 08:30:04.153 +02:00] [INF] [64] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-06-23 08:30:04.823 +02:00] [INF] [72] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "How to Train Your Dragon - The Hidden World (2019)", Path: "/CONTENIDO/PELIS/How to Train Your Dragon - The Hidden World (2019)", Id: 9b15fbf9-190d-6f7f-e862-3406abd1f946
[2025-06-23 08:30:06.339 +02:00] [INF] [152] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-06-23 08:30:07.019 +02:00] [INF] [58] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-23 08:30:07.019 +02:00] [INF] [58] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-23 08:30:07.104 +02:00] [INF] [86] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon - The Hidden World (2019)/How to Train Your Dragon - The Hidden World (2019) Remux-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-06-23 08:30:07.408 +02:00] [ERR] [76] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-06-23 08:30:20.283 +02:00] [INF] [63] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-23 08:30:20.284 +02:00] [INF] [63] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-06-23 08:30:20.295 +02:00] [INF] [63] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-06-23 08:30:20.295 +02:00] [INF] [63] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-06-23 08:30:20.547 +02:00] [INF] [29] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-06-23 08:30:20.547 +02:00] [INF] [29] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-06-23 08:30:20.547 +02:00] [INF] [29] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-06-23 08:30:20.547 +02:00] [INF] [29] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-06-23 08:30:20.547 +02:00] [INF] [29] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-06-23 08:30:20.547 +02:00] [INF] [29] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-06-23 08:30:26.575 +02:00] [INF] [148] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 23 seconds
[2025-06-23 08:30:26.718 +02:00] [INF] [29] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-23 08:30:26.828 +02:00] [INF] [63] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-23 08:30:26.986 +02:00] [INF] [87] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-23 10:25:07.077 +02:00] [WRN] [11] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "172.22.0.1" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-23 10:25:07.124 +02:00] [INF] [11] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.22.0.1" closed
[2025-06-23 12:05:57.897 +02:00] [INF] [17] Emby.Server.Implementations.IO.LibraryMonitor: "Rick y Morty" ("/CONTENIDO/SERIES/Rick and Morty") will be refreshed.
[2025-06-23 12:05:59.224 +02:00] [INF] [91] MediaBrowser.Providers.TV.SeriesMetadataService: Creating Season "Temporada desconocida" entry for "Rick y Morty"
[2025-06-23 12:06:01.416 +02:00] [INF] [91] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 8/Rick and Morty - S08E05 - Cryo Mort a Rickver HDTV-1080p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-06-23 12:06:02.864 +02:00] [INF] [22] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Episode", Name: "Cryo Mort a Rickver", Path: "", Id: 90b49e0c-f489-ad48-7fdd-a1df44ebb0d7
[2025-06-23 12:06:02.953 +02:00] [INF] [22] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season null in series "Rick y Morty"
[2025-06-23 12:06:02.954 +02:00] [INF] [22] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada desconocida", Path: "", Id: 6ed225c1-b48f-10be-fa7e-3694be710444
[2025-06-23 12:06:12.886 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-23 12:06:12.886 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-06-23 12:06:12.886 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-06-23 12:06:12.887 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Update to process
[2025-06-23 12:06:12.887 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-06-23 12:06:12.887 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-06-23 12:06:12.887 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 episodes with event type Update
[2025-06-23 12:06:13.141 +02:00] [INF] [34] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-06-23 12:06:13.141 +02:00] [INF] [34] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-06-23 12:06:13.141 +02:00] [INF] [34] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-06-23 14:09:09.859 +02:00] [INF] [32] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.22.0.1" request
[2025-06-23 14:09:28.242 +02:00] [INF] [84] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-23 14:09:28.405 +02:00] [WRN] [23] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-23 14:09:39.849 +02:00] [WRN] [23] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "172.22.0.1" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-23 14:09:39.854 +02:00] [INF] [23] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "172.22.0.1" closed
[2025-06-23 14:31:33.102 +02:00] [INF] [157] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Episodio 5". Stopped at "1295808" ms
[2025-06-23 17:01:55.995 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-06-23 17:01:56.054 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CONFIG_DIR, /config/config]"]
[2025-06-23 17:01:56.058 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-06-23 17:01:56.059 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-06-23 17:01:56.059 +02:00] [INF] [1] Main: Architecture: X64
[2025-06-23 17:01:56.060 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-06-23 17:01:56.060 +02:00] [INF] [1] Main: User Interactive: True
[2025-06-23 17:01:56.060 +02:00] [INF] [1] Main: Processor count: 12
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-06-23 17:01:56.061 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-06-23 17:01:56.429 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-06-23 17:01:56.719 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-06-23 17:01:56.951 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-06-23 17:01:56.967 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-06-23 17:01:57.019 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-06-23 17:01:57.172 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-23 17:01:57.173 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-06-23 17:01:57.173 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-23 17:01:57.174 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-06-23 17:01:57.175 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-06-23 17:01:57.175 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-06-23 17:01:57.176 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-06-23 17:02:26.070 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-06-23 17:02:26.132 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_LOG_DIR, /config/log]"]
[2025-06-23 17:02:26.134 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-06-23 17:02:26.135 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-06-23 17:02:26.135 +02:00] [INF] [1] Main: Architecture: X64
[2025-06-23 17:02:26.137 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-06-23 17:02:26.137 +02:00] [INF] [1] Main: User Interactive: True
[2025-06-23 17:02:26.137 +02:00] [INF] [1] Main: Processor count: 12
[2025-06-23 17:02:26.137 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-06-23 17:02:26.137 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-06-23 17:02:26.137 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-06-23 17:02:26.138 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-06-23 17:02:26.138 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-06-23 17:02:26.138 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-06-23 17:02:26.138 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-06-23 17:02:26.578 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-06-23 17:02:26.934 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-06-23 17:02:27.110 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-06-23 17:02:27.130 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-06-23 17:02:27.205 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-06-23 17:02:27.382 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-23 17:02:27.383 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-06-23 17:02:27.384 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-23 17:02:27.386 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-06-23 17:02:27.388 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-06-23 17:02:27.388 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-06-23 17:02:27.389 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-06-23 17:06:20.370 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-06-23 17:06:20.437 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_CACHE_DIR, /cache]"]
[2025-06-23 17:06:20.441 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-06-23 17:06:20.442 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-06-23 17:06:20.443 +02:00] [INF] [1] Main: Architecture: X64
[2025-06-23 17:06:20.443 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-06-23 17:06:20.443 +02:00] [INF] [1] Main: User Interactive: True
[2025-06-23 17:06:20.443 +02:00] [INF] [1] Main: Processor count: 12
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-06-23 17:06:20.444 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-06-23 17:06:20.796 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-06-23 17:06:21.061 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-06-23 17:06:21.142 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-06-23 17:06:21.155 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-06-23 17:06:21.180 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-06-23 17:06:21.320 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-23 17:06:21.320 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-06-23 17:06:21.320 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-06-23 17:06:21.324 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-06-23 17:06:21.326 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-06-23 17:06:21.326 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-06-23 17:06:21.326 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-06-23 17:06:34.083 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-06-23 17:06:34.087 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-06-23 17:06:34.088 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "*********"
[2025-06-23 17:06:34.089 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "*********"
[2025-06-23 17:06:34.089 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "*********"
[2025-06-23 17:06:34.119 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "*********"
[2025-06-23 17:06:34.120 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "*********"
[2025-06-23 17:06:34.244 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-06-23 17:06:34.879 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-06-23 17:06:34.926 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-06-23 17:06:34.930 +02:00] [INF] [10] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-23 17:06:34.955 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-24 03:00:00.000 +02:00, which is 09:53:25.0451464 from now.
[2025-06-23 17:06:34.978 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-24 02:00:00.000 +02:00, which is 08:53:25.0213018 from now.
[2025-06-23 17:06:35.130 +02:00] [INF] [11] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-23 17:06:35.177 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-06-23 17:06:35.252 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-06-23 17:06:35.276 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-06-23 17:06:35.309 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-06-23 17:06:35.492 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-06-23 17:06:36.456 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-23 17:06:38.000 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-06-23 17:06:38.022 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-06-23 17:06:39.667 +02:00] [INF] [14] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-06-23 17:06:49.887 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-06-23 17:06:49.905 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "37e9d167aa7248f5a395aa540baf08a6"
[2025-06-23 17:06:49.905 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-06-23 17:06:49.905 +02:00] [INF] [1] Main: Startup complete 0:00:30.2163852
[2025-06-23 17:39:51.606 +02:00] [INF] [113] Jellyfin.Server.Implementations.Users.UserManager: Authentication request for "Tankeeee2_GAMES" has succeeded.
[2025-06-23 17:39:51.610 +02:00] [INF] [113] Emby.Server.Implementations.Session.SessionManager: Current/Max sessions for user "Tankeeee2_GAMES": 0/0
[2025-06-23 17:39:51.614 +02:00] [INF] [113] Emby.Server.Implementations.Session.SessionManager: Logging out access token "ed88c0591fad44f595a549aa0e9114bf"
[2025-06-23 17:39:51.649 +02:00] [INF] [113] Emby.Server.Implementations.Session.SessionManager: Creating new access token for user d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64
