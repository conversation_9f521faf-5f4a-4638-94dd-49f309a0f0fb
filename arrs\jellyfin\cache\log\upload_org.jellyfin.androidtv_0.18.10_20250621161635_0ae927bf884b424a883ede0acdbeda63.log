---
client: <PERSON><PERSON><PERSON> for Android TV
client_version: 0.18.10
client_repository: https://github.com/jellyfin/jellyfin-androidtv
type: crash_report
format: markdown
---
### Logs

***Stack Trace***: 
```log
org.jellyfin.sdk.api.client.exception.TimeoutException: HTTP host unreachable
	at org.jellyfin.sdk.api.ktor.KtorClient.request(KtorClient.kt:151)
	at org.jellyfin.sdk.api.ktor.KtorClient$request$1.invokeSuspend(Unknown Source:19)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
	Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@38a2c04, Dispatchers.IO]
Caused by: java.net.UnknownHostException: Unable to resolve host "tankeflix.duckdns.org": No address associated with hostname
	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:124)
	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
	at java.lang.Thread.run(Thread.java:923)

```  
***Logcat***: 
```log
--------- beginning of main
06-21 18:20:26.676 I/ActivityThemeExtensionsKt(28294): Applying theme DARK
06-21 18:20:26.875 I/StartupActivity$onPermissionsGranted(28294): Found a session in the session repository, waiting for the currentUser in the application class.
06-21 18:20:26.876 I/StartupActivity$onPermissionsGranted(28294): CurrentUser changed to d28e5d2d-16e0-4bf2-96f6-9ddbe9497b64 while waiting for startup.
06-21 18:20:26.896 I/org.jellyfin.sdk.api.ktor.KtorClient(28294): POST https://tankeflix.duckdns.org/Sessions/Capabilities?playableMediaTypes=Video&playableMediaTypes=Audio&supportedCommands=DisplayContent&supportedCommands=SetSubtitleStreamIndex&supportedCommands=SetAudioStreamIndex&supportedCommands=DisplayMessage&supportedCommands=SendString&supportedCommands=VolumeUp&supportedCommands=VolumeDown&supportedCommands=SetVolume&supportedCommands=Mute&supportedCommands=Unmute&supportedCommands=ToggleMute&supportsMediaControl=true&supportsPersistentIdentifier=true
06-21 18:20:27.047 I/org.jellyfin.sdk.api.ktor.KtorClient(28294): GET https://tankeflix.duckdns.org/Shows/NextUp?limit=10&fields=DateCreated&imageTypeLimit=1&enableTotalRecordCount=true&disableFirstEpisode=false&enableResumable=false&enableRewatching=false
06-21 18:20:27.048 I/org.jellyfin.sdk.api.ktor.KtorClient(28294): GET https://tankeflix.duckdns.org/UserItems/Resume?limit=10&fields=DateCreated&mediaTypes=Video&imageTypeLimit=1&includeItemTypes=Episode&includeItemTypes=Movie&enableTotalRecordCount=true&enableImages=true&excludeActiveSessions=true
06-21 18:20:37.019 E/SocketHandler(28294): Unable to update capabilities
06-21 18:20:37.019 E/SocketHandler(28294): org.jellyfin.sdk.api.client.exception.TimeoutException: HTTP host unreachable
06-21 18:20:37.019 E/SocketHandler(28294): 	at org.jellyfin.sdk.api.ktor.KtorClient.request(KtorClient.kt:151)
06-21 18:20:37.019 E/SocketHandler(28294): 	at org.jellyfin.sdk.api.ktor.KtorClient$request$1.invokeSuspend(Unknown Source:19)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:98)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
06-21 18:20:37.019 E/SocketHandler(28294): 	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
06-21 18:20:37.019 E/SocketHandler(28294): Caused by: java.net.UnknownHostException: Unable to resolve host "tankeflix.duckdns.org": No address associated with hostname
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:156)
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:103)
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.net.InetAddress.getAllByName(InetAddress.java:1152)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.Dns$Companion$DnsSystem.lookup(Dns.kt:49)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.RouteSelector.resetNextInetSocketAddress(RouteSelector.kt:169)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.RouteSelector.nextProxy(RouteSelector.kt:131)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.RouteSelector.next(RouteSelector.kt:73)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:205)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
06-21 18:20:37.019 E/SocketHandler(28294): 	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:517)
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.lang.Thread.run(Thread.java:923)
06-21 18:20:37.019 E/SocketHandler(28294): Caused by: android.system.GaiException: android_getaddrinfo failed: EAI_NODATA (No address associated with hostname)
06-21 18:20:37.019 E/SocketHandler(28294): 	at libcore.io.Linux.android_getaddrinfo(Native Method)
06-21 18:20:37.019 E/SocketHandler(28294): 	at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:73)
06-21 18:20:37.019 E/SocketHandler(28294): 	at libcore.io.BlockGuardOs.android_getaddrinfo(BlockGuardOs.java:202)
06-21 18:20:37.019 E/SocketHandler(28294): 	at libcore.io.ForwardingOs.android_getaddrinfo(ForwardingOs.java:73)
06-21 18:20:37.019 E/SocketHandler(28294): 	at java.net.Inet6AddressImpl.lookupHostByName(Inet6AddressImpl.java:135)
06-21 18:20:37.019 E/SocketHandler(28294): 	... 23 more
06-21 18:20:37.129 I/org.jellyfin.sdk.api.ktor.KtorClient(28294): GET https://tankeflix.duckdns.org/LiveTv/Programs/Recommended?limit=1&isAiring=true&imageTypeLimit=1&enableTotalRecordCount=false
06-21 18:20:37.140 I/HomeRowsFragment(28294): Updating audio queue in HomeFragment (onQueueStatusChanged)
06-21 18:20:37.155 E/ACRA    (28294): ACRA caught a TimeoutException for org.jellyfin.androidtv
06-21 18:20:37.161 I/org.jellyfin.sdk.api.ktor.KtorClient(28294): GET https://tankeflix.duckdns.org/Branding/Configuration
06-21 18:20:37.167 I/HomeRowsFragment(28294): Updating audio queue in HomeFragment (onResume)
06-21 18:20:37.241 I/org.jellyfin.sdk.api.ktor.KtorClient(28294): GET https://tankeflix.duckdns.org/System/Info/Public

```  

### App information

***App version***: `0.18.10` (`181099`)  
***Package name***: `org.jellyfin.androidtv`  
***Build***: 
```json
{"BOARD":"karat","BOOTLOADER":"unknown","BRAND":"Amazon","CPU_ABI":"armeabi-v7a","CPU_ABI2":"armeabi","DEVICE":"karat","DISPLAY":"RS8136.3332N","FINGERPRINT":"Amazon\/karat\/karat:11\/RS8136.3332N\/0027314160772:user\/amz-p,release-keys","HARDWARE":"mt8696","HOST":"c5-ri-1804-use1a-b-18-04-fos8-19","ID":"RS8136.3332N","IS_DEBUGGABLE":false,"IS_EMULATOR":false,"MANUFACTURER":"Amazon","MODEL":"AFTKRT","PERMISSIONS_REVIEW_REQUIRED":true,"PRODUCT":"karat","RADIO":"unknown","SUPPORTED_32_BIT_ABIS":["armeabi-v7a","armeabi"],"SUPPORTED_64_BIT_ABIS":[],"SUPPORTED_ABIS":["armeabi-v7a","armeabi"],"TAGS":"amz-p,release-keys","TIME":1746580738000,"TYPE":"user","UNKNOWN":"unknown","USER":"build","VERSION":{"ACTIVE_CODENAMES":[],"BASE_OS":"RP1A.200720.009","CODENAME":"REL","INCREMENTAL":"0027314160772","PREVIEW_SDK_FINGERPRINT":"REL","PREVIEW_SDK_INT":0,"RELEASE":"11","RELEASE_OR_CODENAME":"11","SDK":"30","SDK_INT":30,"SECURITY_PATCH":"2025-01-01"}}
```  
***Build config***: 
```json
{"APPLICATION_ID":"org.jellyfin.androidtv","BUILD_TYPE":"release","DEBUG":false,"DEVELOPMENT":false,"VERSION_CODE":181099,"VERSION_NAME":"0.18.10"}
```  

### Device information

***Android version***: `11`  
***Device brand***: `Amazon`  
***Device product***: `karat`  
***Device model***: `AFTKRT`  

### Crash information

***Start time***: `2025-06-20T20:49:59.926+02:00`  
***Crash time***: `2025-06-21T18:20:37.405+02:00`  

