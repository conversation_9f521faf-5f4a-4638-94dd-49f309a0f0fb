{"Protocol":0,"Id":"5e228edb9c433e2a80ff2bfcc8d1e7cb","Path":"/CONTENIDO/SERIES/<PERSON> and <PERSON><PERSON><PERSON>/Season 4/<PERSON> and <PERSON><PERSON><PERSON> - S04E03 - One Crew Over the Crewcoo\u0027s Morty HDTV-720p.mp4","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mov,mp4,m4a,3gp,3g2,mj2","Size":245901426,"Name":"<PERSON> and <PERSON><PERSON><PERSON> - S04E03 - One Crew Over the Crewcoo\u0027s Morty HDTV-720p","IsRemote":false,"ETag":"c0e7e4ef73add47f21287b26712ffe90","RunTimeTicks":13020800000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"h264","CodecTag":"avc1","Language":"und","ColorRange":null,"ColorSpace":"bt709","ColorTransfer":"bt709","ColorPrimaries":"bt709","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/12800","CodecTimeBase":null,"Title":null,"VideoRange":1,"VideoRangeType":1,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"720p H264 SDR","NalLengthSize":"4","IsInterlaced":false,"IsAVC":true,"ChannelLayout":null,"BitRate":1072418,"BitDepth":8,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":720,"Width":1280,"AverageFrameRate":25,"RealFrameRate":25,"ReferenceFrameRate":25,"Profile":"High","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p","Level":31,"IsAnamorphic":false},{"Codec":"eac3","CodecTag":"ec-3","Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital\u002B - Stereo - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"stereo","BitRate":128000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":2,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"eac3","CodecTag":"ec-3","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/48000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - Dolby Digital\u002B - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":256000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":114,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"mov_text","CodecTag":"tx3g","Language":"und","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Und - Predeterminado - MOV_TEXT","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":116,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"png","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"gbr","ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/90000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":null,"NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":null,"RealFrameRate":90000,"ReferenceFrameRate":90000,"Profile":null,"Type":3,"AspectRatio":"16:9","Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"rgb24","Level":-99,"IsAnamorphic":false}],"MediaAttachments":[{"Codec":"png","CodecTag":"[0][0][0][0]","Comment":null,"Index":5,"FileName":null,"MimeType":null,"DeliveryUrl":null}],"Formats":[],"Bitrate":1510822,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G -fflags +genpts  -i file:"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E03 - One Crew Over the Crewcoo's Morty HDTV-720p.mp4" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/d668751000111c994a10f32ae6da8812%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/d668751000111c994a10f32ae6da8812.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
[mov,mp4,m4a,3gp,3g2,mj2 @ 0x559457776340] stream 0, timescale not set
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'file:/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E03 - One Crew Over the Crewcoo's Morty HDTV-720p.mp4':
  Metadata:
    major_brand     : isom
    minor_version   : 512
    compatible_brands: isomdby1iso2avc1mp41
    title           : One Crew Over The Crewcoo's Morty
    date            : 2024
    encoder         : Lavf61.9.106
    description     : Muchos giros e imprevistos, bro. Poneos el casco.
    show            : Rick y Morty
    episode_id      : 3
    season_number   : 4
  Duration: 00:21:42.08, start: 0.000000, bitrate: 1510 kb/s
  Stream #0:0[0x1](und): Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], 1072 kb/s, 25 fps, 25 tbr, 12800 tbn (default)
      Metadata:
        handler_name    : VideoHandler
        vendor_id       : [0][0][0][0]
        encoder         : AVC Coding
  Stream #0:1[0x3](spa): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:2[0x4](eng): Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, 5.1(side), fltp, 256 kb/s (default)
      Metadata:
        handler_name    : SoundHandler
        vendor_id       : [0][0][0][0]
      Side data:
        audio service type: main
  Stream #0:3[0x5](eng): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:4[0x6](und): Subtitle: mov_text (tx3g / 0x67337874), 0 kb/s (default)
      Metadata:
        handler_name    : SubtitleHandler
  Stream #0:5[0x0]: Video: png, rgb24(pc, gbr/unknown/unknown), 3840x2160 [SAR 3780:3780 DAR 16:9], 90k tbr, 90k tbn (attached pic)
[out#0/hls @ 0x5594577d0340] Codec AVOption threads (set the number of threads) has not been used for any stream. The most likely reason is either wrong type (e.g. a video option with no video streams) or that it is a private option of some encoder which was not actually used for any stream.
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
  Stream #0:1 -> #0:1 (copy)
Output #0, hls, to '/cache/transcodes/d668751000111c994a10f32ae6da8812.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264 (High) (avc1 / 0x31637661), yuv420p(tv, bt709, progressive), 1280x720 [SAR 1:1 DAR 16:9], q=2-31, 1072 kb/s, 25 fps, 25 tbr, 90k tbn (default)
  Stream #0:1: Audio: eac3 (ec-3 / 0x332D6365), 48000 Hz, stereo, fltp, 128 kb/s (default)
      Side data:
        audio service type: main
Press [q] to stop, [?] for help
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88120.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88121.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88122.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88123.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88124.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88125.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88126.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88127.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88128.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da88129.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881210.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881211.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881212.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881213.ts' for writing
size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881214.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881215.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881216.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881217.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881218.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881219.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881220.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881221.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881222.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881223.ts' for writing
size=N/A time=00:01:07.07 bitrate=N/A speed=67.1x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881224.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881225.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881226.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881227.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881228.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881229.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881230.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881231.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881232.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881233.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881234.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881235.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881236.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881237.ts' for writing
size=N/A time=00:02:31.80 bitrate=N/A speed= 101x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881238.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881239.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881240.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881241.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881242.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881243.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881244.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881245.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881246.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881247.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881248.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881249.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881250.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881251.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881252.ts' for writing
size=N/A time=00:03:56.00 bitrate=N/A speed= 118x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881253.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881254.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881255.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881256.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881257.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881258.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881259.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881260.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881261.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881262.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881263.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881264.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881265.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881266.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881267.ts' for writing
size=N/A time=00:05:24.00 bitrate=N/A speed= 130x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881268.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881269.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881270.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881271.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881272.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881273.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881274.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881275.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881276.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881277.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881278.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881279.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881280.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881281.ts' for writing
size=N/A time=00:06:50.27 bitrate=N/A speed= 137x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881282.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881283.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881284.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881285.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881286.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881287.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881288.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881289.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881290.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881291.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881292.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881293.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881294.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881295.ts' for writing
size=N/A time=00:08:21.12 bitrate=N/A speed= 143x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881296.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881297.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881298.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da881299.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812100.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812101.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812102.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812103.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812104.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812105.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812106.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812107.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812108.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812109.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812110.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812111.ts' for writing
size=N/A time=00:09:49.31 bitrate=N/A speed= 147x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812112.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812113.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812114.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812115.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812116.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812117.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812118.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812119.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812120.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812121.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812122.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812123.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812124.ts' for writing
size=N/A time=00:11:09.31 bitrate=N/A speed= 149x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812125.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812126.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812127.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812128.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812129.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812130.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812131.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812132.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812133.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812134.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812135.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812136.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812137.ts' for writing
size=N/A time=00:12:30.91 bitrate=N/A speed= 150x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812138.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812139.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812140.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812141.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812142.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812143.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812144.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812145.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812146.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812147.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812148.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812149.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812150.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812151.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812152.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812153.ts' for writing
size=N/A time=00:14:01.31 bitrate=N/A speed= 153x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812154.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812155.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812156.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812157.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812158.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812159.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812160.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812161.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812162.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812163.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812164.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812165.ts' for writing
size=N/A time=00:15:17.60 bitrate=N/A speed= 153x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812166.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812167.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812168.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812169.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812170.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812171.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812172.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812173.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812174.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812175.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812176.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812177.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812178.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812179.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812180.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812181.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812182.ts' for writing
size=N/A time=00:16:57.31 bitrate=N/A speed= 156x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812183.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812184.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812185.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812186.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812187.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812188.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812189.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812190.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812191.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812192.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812193.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812194.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812195.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812196.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812197.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812198.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812199.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812200.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812201.ts' for writing
size=N/A time=00:18:54.56 bitrate=N/A speed= 162x    
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812202.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812203.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812204.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812205.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812206.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812207.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812208.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812209.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812210.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812211.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812212.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812213.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812214.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812215.ts' for writing
[hls @ 0x5594577d0f40] Opening '/cache/transcodes/d668751000111c994a10f32ae6da8812216.ts' for writing
[out#0/hls @ 0x5594577d0340] video:170440KiB audio:20345KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
size=N/A time=00:20:17.90 bitrate=N/A speed= 166x    