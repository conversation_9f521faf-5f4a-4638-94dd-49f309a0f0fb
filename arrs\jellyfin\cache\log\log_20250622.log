[2025-06-22 00:09:07.562 +02:00] [INF] [60] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 00:09:07.569 +02:00] [INF] [60] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 00:09:07.623 +02:00] [INF] [60] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-22 01:59:59.464 +02:00] [INF] [11] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-06-22 01:59:59.564 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-06-22 02:00:00.151 +02:00] [INF] [11] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-06-23 02:00:00.000 +02:00, which is 23:59:59.8487572 from now.
[2025-06-22 02:59:59.064 +02:00] [INF] [62] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-06-22 02:59:59.992 +02:00] [INF] [62] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-22 03:00:00.000 +02:00, which is 00:00:00.0077270 from now.
[2025-06-22 03:00:00.021 +02:00] [INF] [34] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-06-22 03:00:00.999 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-06-23 03:00:00.000 +02:00, which is 23:59:59.0001474 from now.
[2025-06-22 07:59:49.511 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 07:59:49.512 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 07:59:49.516 +02:00] [INF] [14] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-22 08:28:04.621 +02:00] [INF] [51] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-22 08:28:04.632 +02:00] [INF] [51] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-06-22 08:28:04.632 +02:00] [INF] [51] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-06-22 08:28:04.654 +02:00] [INF] [51] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-06-22 08:28:05.510 +02:00] [WRN] [51] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-22 08:28:05.807 +02:00] [INF] [55] Jellyfin.Drawing.ImageProcessor: Creating image collage and saving to "/tmp/jellyfin/b85c5b5ec90b4c0ba832b1df12abd215.png"
[2025-06-22 08:28:05.911 +02:00] [INF] [50] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 1 seconds
[2025-06-22 08:28:06.261 +02:00] [INF] [55] Jellyfin.Drawing.ImageProcessor: Completed creation of image collage and saved to "/tmp/jellyfin/b85c5b5ec90b4c0ba832b1df12abd215.png"
[2025-06-22 08:28:06.877 +02:00] [WRN] [55] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-22 08:28:09.359 +02:00] [INF] [67] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 08:28:09.463 +02:00] [INF] [67] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-06-22 08:28:10.055 +02:00] [INF] [31] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-22 08:28:10.055 +02:00] [INF] [31] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 08:28:11.230 +02:00] [ERR] [66] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-06-22 08:28:26.316 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 21 seconds
[2025-06-22 08:28:26.321 +02:00] [INF] [47] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-22 08:28:26.463 +02:00] [INF] [68] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-22 08:28:26.628 +02:00] [INF] [41] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-22 08:32:07.783 +02:00] [INF] [75] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 08:32:16.550 +02:00] [INF] [76] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 08:32:16.667 +02:00] [WRN] [85] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 08:32:16.794 +02:00] [INF] [76] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E09 - The ABC's of Beth HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699.m3u8\""
[2025-06-22 08:32:22.552 +02:00] [INF] [86] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 08:32:23.673 +02:00] [INF] [85] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/33072ed917cb8cc8b4f7ea70bef3d699.m3u8"
[2025-06-22 08:32:25.364 +02:00] [INF] [84] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "El ABC de Beth". Stopped at "5576" ms
[2025-06-22 08:32:26.710 +02:00] [INF] [85] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 08:32:26.795 +02:00] [WRN] [85] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 08:32:26.850 +02:00] [INF] [86] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E10 - The Wedding Squanchers HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/581f7dad8af84504f254a00eefe8d30b%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/581f7dad8af84504f254a00eefe8d30b.m3u8\""
[2025-06-22 08:32:33.549 +02:00] [INF] [80] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 08:32:37.776 +02:00] [WRN] [85] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 08:32:37.782 +02:00] [INF] [85] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 08:34:20.120 +02:00] [INF] [48] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/581f7dad8af84504f254a00eefe8d30b.m3u8"
[2025-06-22 08:34:21.792 +02:00] [INF] [78] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "De squanch en squanch". Stopped at "112141" ms
[2025-06-22 08:34:31.980 +02:00] [INF] [92] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 08:34:32.089 +02:00] [WRN] [92] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 08:34:32.136 +02:00] [INF] [56] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E07 - Promortyus HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8\""
[2025-06-22 08:34:35.592 +02:00] [INF] [92] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8"
[2025-06-22 08:34:35.647 +02:00] [INF] [92] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 08:34:35.704 +02:00] [INF] [92] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -ss 00:08:06.500 -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E07 - Promortyus HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 81 -hls_segment_filename \"/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8\""
[2025-06-22 08:34:38.660 +02:00] [INF] [44] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8"
[2025-06-22 08:34:39.019 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 08:34:39.019 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/d0f9740b44fcb05c896b4bbe7d1fbd86.m3u8"
[2025-06-22 08:34:40.674 +02:00] [INF] [44] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Promortyus". Stopped at "237886" ms
[2025-06-22 08:34:44.243 +02:00] [INF] [92] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 08:34:44.325 +02:00] [WRN] [44] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 08:34:44.360 +02:00] [INF] [44] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E06 - The Ricks Must Be Crazy HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3.m3u8\""
[2025-06-22 08:34:51.456 +02:00] [INF] [56] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 08:36:20.723 +02:00] [INF] [92] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/5c2d1cf92607da1dfdd75ffb216413a3.m3u8"
[2025-06-22 08:36:22.394 +02:00] [INF] [92] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Los Ricks deben estar locos". Stopped at "95320" ms
[2025-06-22 08:38:15.435 +02:00] [INF] [99] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 08:38:45.428 +02:00] [WRN] [99] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 08:38:45.432 +02:00] [INF] [99] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 08:38:49.544 +02:00] [INF] [96] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 08:39:05.316 +02:00] [INF] [92] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 08:39:05.387 +02:00] [WRN] [96] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 08:39:05.506 +02:00] [INF] [92] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E10 - Star Mort - Rickturn of the Jerri HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/099d9a72eed3fb3bec80405e641b5697%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/099d9a72eed3fb3bec80405e641b5697.m3u8\""
[2025-06-22 08:39:11.501 +02:00] [INF] [99] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 08:39:19.540 +02:00] [WRN] [99] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 08:39:19.544 +02:00] [INF] [99] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 08:40:31.092 +02:00] [INF] [103] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/099d9a72eed3fb3bec80405e641b5697.m3u8"
[2025-06-22 08:40:32.770 +02:00] [INF] [96] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Estrella de Mort: Ricktorno del Jerri". Stopped at "84257" ms
[2025-06-22 08:40:42.004 +02:00] [INF] [91] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 08:40:42.093 +02:00] [WRN] [91] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 08:40:42.124 +02:00] [INF] [100] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E01 - Pilot HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/f1aefa999655e32aec909d5afea40c27%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/f1aefa999655e32aec909d5afea40c27.m3u8\""
[2025-06-22 08:40:48.389 +02:00] [INF] [96] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 09:01:50.944 +02:00] [INF] [101] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/f1aefa999655e32aec909d5afea40c27.m3u8"
[2025-06-22 09:01:52.594 +02:00] [INF] [61] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Piloto". Stopped at "1267878" ms
[2025-06-22 09:04:22.207 +02:00] [INF] [78] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 09:07:42.908 +02:00] [INF] [78] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 09:08:42.903 +02:00] [INF] [100] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 09:09:30.906 +02:00] [INF] [100] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 09:09:42.904 +02:00] [INF] [95] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 09:09:54.906 +02:00] [INF] [103] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-06-22 09:11:15.888 +02:00] [WRN] [32] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 09:11:15.892 +02:00] [INF] [32] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 10:03:44.352 +02:00] [INF] [88] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 10:04:14.349 +02:00] [WRN] [100] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 10:04:14.353 +02:00] [INF] [100] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 10:04:16.968 +02:00] [INF] [83] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 10:04:46.966 +02:00] [WRN] [86] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 10:04:46.968 +02:00] [INF] [86] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 10:05:59.182 +02:00] [INF] [32] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 10:07:26.764 +02:00] [INF] [96] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 10:07:26.881 +02:00] [WRN] [82] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 10:07:27.386 +02:00] [INF] [97] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 2/Rick and Morty - S02E08 - Interdimensional Cable 2 - Tempting Fate HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/4fc29f30d90f0c004332b81d05d92360%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/4fc29f30d90f0c004332b81d05d92360.m3u8\""
[2025-06-22 10:07:33.993 +02:00] [INF] [96] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 10:20:54.728 +02:00] [INF] [94] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 10:21:06.717 +02:00] [INF] [89] Emby.Server.Implementations.Session.SessionWebSocketListener: Lost 1 WebSockets.
[2025-06-22 10:21:15.559 +02:00] [WRN] [94] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 10:21:15.562 +02:00] [INF] [94] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 10:30:08.683 +02:00] [INF] [94] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 10:30:08.690 +02:00] [INF] [94] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/4fc29f30d90f0c004332b81d05d92360.m3u8"
[2025-06-22 10:30:08.874 +02:00] [INF] [93] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 1/Rick and Morty - S01E06 - Rick Potion #9 HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/68faae809bf248b64de429683c22da4e%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/68faae809bf248b64de429683c22da4e.m3u8\""
[2025-06-22 10:30:10.450 +02:00] [INF] [94] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Cable interdimensional 2: Tentando al destino". Stopped at "1295008" ms
[2025-06-22 10:30:14.984 +02:00] [INF] [93] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 10:30:19.979 +02:00] [WRN] [93] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 10:31:14.790 +02:00] [INF] [78] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/68faae809bf248b64de429683c22da4e.m3u8"
[2025-06-22 10:31:16.440 +02:00] [INF] [78] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Poción de Rick nº 9". Stopped at "64567" ms
[2025-06-22 13:39:29.679 +02:00] [INF] [30] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 13:39:55.116 +02:00] [WRN] [3] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 13:39:55.135 +02:00] [INF] [3] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 13:40:38.013 +02:00] [INF] [42] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 13:40:38.165 +02:00] [WRN] [3] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 13:40:38.611 +02:00] [INF] [42] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 3/Rick and Morty - S03E06 - Rest and Ricklaxation HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/5edbce0918324626e228678796ac5a31%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/5edbce0918324626e228678796ac5a31.m3u8\""
[2025-06-22 13:40:46.777 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 13:40:48.255 +02:00] [INF] [30] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/5edbce0918324626e228678796ac5a31.m3u8"
[2025-06-22 13:40:48.261 +02:00] [INF] [3] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 13:40:49.030 +02:00] [INF] [30] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Rick: una Mort plena". Stopped at "0" ms
[2025-06-22 13:40:49.984 +02:00] [INF] [30] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Descanso y Ricklajación". Stopped at "1303104" ms
[2025-06-22 13:40:54.114 +02:00] [INF] [3] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 13:40:54.216 +02:00] [WRN] [3] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 13:40:54.523 +02:00] [INF] [89] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G -fflags +genpts  -i file:\"/CONTENIDO/SERIES/Rick and Morty/Season 4/Rick and Morty - S04E03 - One Crew Over the Crewcoo's Morty HDTV-720p.mp4\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 copy -bsf:v h264_mp4toannexb -start_at_zero -codec:a:0 copy -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 6 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/d668751000111c994a10f32ae6da8812%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/d668751000111c994a10f32ae6da8812.m3u8\""
[2025-06-22 13:41:02.369 +02:00] [INF] [3] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 13:41:08.525 +02:00] [INF] [90] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/d668751000111c994a10f32ae6da8812.m3u8"
[2025-06-22 13:41:10.274 +02:00] [INF] [90] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Algún equipo voló sobre el equipillo del Morty". Stopped at "883" ms
[2025-06-22 13:52:48.761 +02:00] [INF] [85] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 13:52:48.761 +02:00] [INF] [85] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 13:52:48.766 +02:00] [INF] [85] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-22 14:43:15.785 +02:00] [INF] [32] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 14:43:27.680 +02:00] [INF] [85] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:44:03.049 +02:00] [INF] [81] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "La Patrulla Canina: La superpelícula". Stopped at "33624" ms
[2025-06-22 14:49:17.596 +02:00] [INF] [93] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 14:49:43.887 +02:00] [INF] [56] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-22 14:49:43.888 +02:00] [INF] [56] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-06-22 14:49:43.888 +02:00] [INF] [56] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-06-22 14:49:43.888 +02:00] [INF] [56] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-06-22 14:49:44.320 +02:00] [WRN] [56] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-22 14:49:45.250 +02:00] [WRN] [56] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-22 14:49:46.163 +02:00] [INF] [78] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Folder", Name: "How to Train Your Dragon 2 (2014)", Path: "/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)", Id: 1aa67a43-fbdd-d588-13fa-73fd34b5df03
[2025-06-22 14:49:47.400 +02:00] [INF] [65] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 14:49:47.457 +02:00] [INF] [65] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-06-22 14:49:48.040 +02:00] [INF] [23] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-22 14:49:48.040 +02:00] [INF] [23] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 14:49:48.412 +02:00] [INF] [21] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-06-22 14:49:48.536 +02:00] [ERR] [86] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-06-22 14:49:48.932 +02:00] [INF] [23] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-22 14:49:48.932 +02:00] [INF] [23] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 14:49:56.298 +02:00] [INF] [20] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-22 14:49:56.299 +02:00] [INF] [20] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-06-22 14:50:07.090 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-22 14:50:07.096 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-06-22 14:50:07.096 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-06-22 14:50:07.096 +02:00] [INF] [91] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-06-22 14:50:07.361 +02:00] [INF] [70] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-06-22 14:50:07.361 +02:00] [INF] [70] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-06-22 14:50:07.361 +02:00] [INF] [70] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-06-22 14:50:07.364 +02:00] [INF] [70] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-06-22 14:50:07.364 +02:00] [INF] [70] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-06-22 14:50:07.364 +02:00] [INF] [70] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-06-22 14:50:12.217 +02:00] [INF] [73] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 28 seconds
[2025-06-22 14:50:12.220 +02:00] [INF] [91] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-22 14:50:12.411 +02:00] [INF] [70] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-22 14:50:12.633 +02:00] [INF] [59] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-22 14:51:35.649 +02:00] [INF] [70] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 14:52:05.649 +02:00] [WRN] [28] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 14:52:05.652 +02:00] [INF] [28] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 14:52:32.229 +02:00] [INF] [23] Jellyfin.Api.Controllers.ItemLookupController: Setting provider id's to item 14857cd7-81a7-139a-b1db-7a466b13d843-"Cómo entrenar a tu dragón 2": [("Imdb": "tt1646971"), ("Tmdb": "82702"), ("Tvdb": "149")]
[2025-06-22 14:52:32.260 +02:00] [INF] [23] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Starting "/usr/lib/jellyfin-ffmpeg/ffprobe" with args "-analyzeduration 200M -probesize 1G -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -threads 0 -v warning -print_format json -show_streams -show_chapters -show_format"
[2025-06-22 14:52:47.152 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-22 14:52:47.152 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Remove to process
[2025-06-22 14:52:47.152 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No movies with event type Add to process
[2025-06-22 14:52:47.152 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: Processing 1 movies with event type Update
[2025-06-22 14:52:47.333 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Remove to process
[2025-06-22 14:52:47.333 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Add to process
[2025-06-22 14:52:47.333 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No episodes with event type Update to process
[2025-06-22 14:52:47.333 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Remove to process
[2025-06-22 14:52:47.333 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Add to process
[2025-06-22 14:52:47.333 +02:00] [INF] [23] Trakt.Helpers.LibraryManagerEventsHelper: No shows with event type Update to process
[2025-06-22 14:53:06.025 +02:00] [INF] [34] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 14:53:18.022 +02:00] [INF] [23] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 14:53:25.189 +02:00] [WRN] [23] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 14:53:25.195 +02:00] [INF] [23] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 14:53:53.886 +02:00] [INF] [68] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 14:54:07.203 +02:00] [INF] [34] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:54:08.143 +02:00] [INF] [68] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G  -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 14232000 -bufsize 28464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 \"expr:gte(t,n_forced*3)\" -sc_threshold:v:0 0 -vf \"setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,2560)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p\" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/3ea13e46df7990deade63628f239e8fa%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/3ea13e46df7990deade63628f239e8fa.m3u8\""
[2025-06-22 14:54:13.861 +02:00] [INF] [53] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/3ea13e46df7990deade63628f239e8fa.m3u8"
[2025-06-22 14:54:13.872 +02:00] [ERR] [34] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "A task was canceled". URL "GET" "/videos/14857cd7-81a7-139a-b1db-7a466b13d843/hls1/main/0.ts".
[2025-06-22 14:54:15.066 +02:00] [INF] [53] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 14:54:15.066 +02:00] [INF] [53] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/3ea13e46df7990deade63628f239e8fa.m3u8"
[2025-06-22 14:54:16.575 +02:00] [INF] [57] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "0" ms
[2025-06-22 14:54:17.396 +02:00] [INF] [53] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:54:17.954 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G  -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 14232000 -bufsize 28464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 \"expr:gte(t,n_forced*3)\" -sc_threshold:v:0 0 -vf \"setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,2560)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p\" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b.m3u8\""
[2025-06-22 14:54:23.882 +02:00] [WRN] [34] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 14:54:23.888 +02:00] [INF] [34] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 14:54:25.970 +02:00] [INF] [68] MediaBrowser.MediaEncoding.Subtitles.SubtitleEncoder: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -copyts -map 0:4 -an -vn -c:s copy \"/config/data/subtitles/1/163b270a-d228-04a0-e623-c2369fe5924b.srt\" -map 0:5 -an -vn -c:s copy \"/config/data/subtitles/4/43e68cf4-b157-e904-7e7b-a1b28f2be388.srt\" -map 0:6 -an -vn -c:s copy \"/config/data/subtitles/2/259af545-7c67-5a11-5b58-c99e2aa12df3.sup\""
[2025-06-22 14:54:42.065 +02:00] [INF] [68] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b.m3u8"
[2025-06-22 14:54:42.091 +02:00] [INF] [53] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:54:42.922 +02:00] [INF] [68] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 14:54:42.922 +02:00] [INF] [68] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b.m3u8"
[2025-06-22 14:54:44.485 +02:00] [INF] [68] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "5751" ms
[2025-06-22 14:54:46.439 +02:00] [WRN] [53] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 14:54:50.040 +02:00] [INF] [68] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "11150" ms
[2025-06-22 14:54:50.095 +02:00] [INF] [34] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:54:55.742 +02:00] [INF] [57] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "14869" ms
[2025-06-22 14:54:55.824 +02:00] [INF] [34] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:54:56.444 +02:00] [INF] [27] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G  -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 19232000 -bufsize 38464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 \"expr:gte(t,n_forced*3)\" -sc_threshold:v:0 0 -vf \"setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,2560)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p\" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/3b7d47b7c86c456468cacdc0c9eaf62e%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/3b7d47b7c86c456468cacdc0c9eaf62e.m3u8\""
[2025-06-22 14:55:03.841 +02:00] [INF] [34] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 14:55:27.050 +02:00] [INF] [34] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/3b7d47b7c86c456468cacdc0c9eaf62e.m3u8"
[2025-06-22 14:55:27.071 +02:00] [INF] [28] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:55:27.646 +02:00] [INF] [60] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G  -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 39232000 -bufsize 78464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 \"expr:gte(t,n_forced*3)\" -sc_threshold:v:0 0 -vf \"setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,3840)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p\" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/a2d8ec925a5c0f15256cffacb36b61ab%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/a2d8ec925a5c0f15256cffacb36b61ab.m3u8\""
[2025-06-22 14:55:28.101 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 14:55:28.101 +02:00] [INF] [34] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/3b7d47b7c86c456468cacdc0c9eaf62e.m3u8"
[2025-06-22 14:55:29.698 +02:00] [INF] [60] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "14869" ms
[2025-06-22 14:55:33.817 +02:00] [WRN] [57] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 14:55:33.821 +02:00] [INF] [57] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 14:55:46.444 +02:00] [WRN] [35] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 14:56:11.232 +02:00] [INF] [57] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/a2d8ec925a5c0f15256cffacb36b61ab.m3u8"
[2025-06-22 14:56:12.389 +02:00] [INF] [57] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 14:56:12.389 +02:00] [INF] [57] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/a2d8ec925a5c0f15256cffacb36b61ab.m3u8"
[2025-06-22 14:56:13.926 +02:00] [INF] [60] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "14869" ms
[2025-06-22 14:56:14.487 +02:00] [INF] [57] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 14:56:15.041 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G  -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 39232000 -bufsize 78464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 \"expr:gte(t,n_forced*3)\" -sc_threshold:v:0 0 -vf \"setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,3840)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p\" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/20b2f81a44956caad5934c6a752da6d6%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/20b2f81a44956caad5934c6a752da6d6.m3u8\""
[2025-06-22 15:02:50.857 +02:00] [INF] [55] MediaBrowser.Controller.MediaEncoding.TranscodingJob: Stopping ffmpeg process with q command for "/cache/transcodes/20b2f81a44956caad5934c6a752da6d6.m3u8"
[2025-06-22 15:02:52.076 +02:00] [INF] [55] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 15:02:52.077 +02:00] [INF] [55] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/20b2f81a44956caad5934c6a752da6d6.m3u8"
[2025-06-22 15:02:53.694 +02:00] [INF] [27] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "118896" ms
[2025-06-22 15:03:01.795 +02:00] [INF] [55] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 15:03:02.595 +02:00] [INF] [28] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: "/usr/lib/jellyfin-ffmpeg/ffmpeg" "-analyzeduration 200M -probesize 1G  -i file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 39232000 -bufsize 78464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 \"expr:gte(t,n_forced*3)\" -sc_threshold:v:0 0 -vf \"setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,3840)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p\" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename \"/cache/transcodes/eaf26901fd5fbb436550b4e6b928b422%d.ts\" -hls_playlist_type vod -hls_list_size 0 -y \"/cache/transcodes/eaf26901fd5fbb436550b4e6b928b422.m3u8\""
[2025-06-22 15:07:13.997 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Subtitles.SubtitleEncoder: ffmpeg subtitle extraction completed for "file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\"" to "/config/data/subtitles/1/163b270a-d228-04a0-e623-c2369fe5924b.srt"
[2025-06-22 15:07:13.999 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Subtitles.SubtitleEncoder: ffmpeg subtitle extraction completed for "file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\"" to "/config/data/subtitles/4/43e68cf4-b157-e904-7e7b-a1b28f2be388.srt"
[2025-06-22 15:07:14.000 +02:00] [INF] [37] MediaBrowser.MediaEncoding.Subtitles.SubtitleEncoder: ffmpeg subtitle extraction completed for "file:\"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv\"" to "/config/data/subtitles/2/259af545-7c67-5a11-5b58-c99e2aa12df3.sup"
[2025-06-22 16:22:12.712 +02:00] [INF] [59] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: FFmpeg exited with code 0
[2025-06-22 16:51:17.704 +02:00] [INF] [25] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 16:51:26.145 +02:00] [INF] [55] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Cómo entrenar a tu dragón", Path: "/CONTENIDO/PELIS/How to Train Your Dragon (2010)/How to Train Your Dragon (2010) Remux-2160p.mkv", Id: 0b6fd06e-089b-b8c1-f230-df35550c9ac8
[2025-06-22 16:51:26.181 +02:00] [INF] [55] Emby.Server.Implementations.Library.LibraryManager: Deleting item path, Type: "Movie", Name: "Cómo entrenar a tu dragón", Path: "/CONTENIDO/PELIS/How to Train Your Dragon (2010)", Id: 0b6fd06e-089b-b8c1-f230-df35550c9ac8
[2025-06-22 16:51:36.725 +02:00] [INF] [55] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-22 16:51:36.725 +02:00] [INF] [55] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-06-22 16:51:47.702 +02:00] [WRN] [55] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 16:51:47.707 +02:00] [INF] [55] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 16:55:27.263 +02:00] [INF] [25] MediaBrowser.MediaEncoding.Transcoding.TranscodeManager: Deleting partial stream file(s) "/cache/transcodes/eaf26901fd5fbb436550b4e6b928b422.m3u8"
[2025-06-22 16:55:30.288 +02:00] [INF] [41] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Cómo entrenar a tu dragón 2". Stopped at "5672899" ms
[2025-06-22 17:51:23.125 +02:00] [INF] [24] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 17:51:34.609 +02:00] [INF] [45] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 17:51:34.783 +02:00] [WRN] [24] Trakt.ServerMediator: Received playback progress from user "Tankeeee2_GAMES" but initial state was never set - setting it now!
[2025-06-22 17:51:34.977 +02:00] [ERR] [62] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Could not find file '/CONTENIDO/PELIS/Final Destination (2000)/Destino.final.(2000).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv'". URL "GET" "/Videos/789b5d16-fd14-1587-eecb-8737c042bea0/stream".
[2025-06-22 17:51:35.010 +02:00] [ERR] [62] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Could not find file '/CONTENIDO/PELIS/Final Destination (2000)/Destino.final.(2000).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv'". URL "GET" "/Videos/789b5d16-fd14-1587-eecb-8737c042bea0/stream".
[2025-06-22 17:51:36.046 +02:00] [ERR] [62] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Could not find file '/CONTENIDO/PELIS/Final Destination (2000)/Destino.final.(2000).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv'". URL "GET" "/Videos/789b5d16-fd14-1587-eecb-8737c042bea0/stream".
[2025-06-22 17:51:38.115 +02:00] [ERR] [11] Jellyfin.Api.Middleware.ExceptionMiddleware: Error processing request: "Could not find file '/CONTENIDO/PELIS/Final Destination (2000)/Destino.final.(2000).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv'". URL "GET" "/Videos/789b5d16-fd14-1587-eecb-8737c042bea0/stream".
[2025-06-22 17:51:39.536 +02:00] [INF] [11] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Jellyfin Android" "2.6.3" playing "Destino final". Stopped at "0" ms
[2025-06-22 17:53:41.475 +02:00] [INF] [46] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 17:53:53.473 +02:00] [INF] [46] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 17:54:04.329 +02:00] [INF] [16] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 18:57:30.056 +02:00] [INF] [22] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 18:57:31.831 +02:00] [INF] [87] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Destino final". Stopped at "0" ms
[2025-06-22 19:04:19.242 +02:00] [INF] [11] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 19:04:20.737 +02:00] [INF] [37] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "El Rickevangelio de Gotron Jerrysis". Stopped at "0" ms
[2025-06-22 19:04:20.906 +02:00] [INF] [24] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 19:32:08.107 +02:00] [INF] [11] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "El Rickevangelio de Gotron Jerrysis". Stopped at "1263049" ms
[2025-06-22 19:32:15.416 +02:00] [INF] [87] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 19:32:19.802 +02:00] [INF] [37] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Rickterno amisplandor del Mort sin recuerdos". Stopped at "0" ms
[2025-06-22 19:32:19.873 +02:00] [INF] [44] Jellyfin.Api.Helpers.MediaInfoHelper: User policy for "Tankeeee2_GAMES". EnablePlaybackRemuxing: True EnableVideoPlaybackTranscoding: True EnableAudioPlaybackTranscoding: True
[2025-06-22 19:57:42.439 +02:00] [INF] [44] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.10" playing "Rickterno amisplandor del Mort sin recuerdos". Stopped at "1219217" ms
[2025-06-22 19:58:27.012 +02:00] [WRN] [45] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 19:58:27.017 +02:00] [INF] [45] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 20:23:05.378 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 20:23:05.378 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 20:23:05.386 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-22 20:28:02.523 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar los subtítulos que faltan" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:02.697 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Descargar letras faltantes" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:02.753 +02:00] [INF] [69] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:02.815 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: Optimizing and vacuuming jellyfin.db...
[2025-06-22 20:28:02.829 +02:00] [INF] [35] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Normalización de audio" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:02.879 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "TasksRefreshChannels" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:02.911 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar registros" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:03.059 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.Tasks.OptimizeDatabaseTask: jellyfin.db optimized successfully!
[2025-06-22 20:28:03.059 +02:00] [INF] [60] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Optimizar la base de datos" Completed after 0 minute(s) and 0 seconds
[2025-06-22 20:28:04.374 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 1 seconds
[2025-06-22 20:28:05.254 +02:00] [INF] [91] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Eliminar archivos temporales" Completed after 0 minute(s) and 2 seconds
[2025-06-22 20:28:06.025 +02:00] [INF] [19] Jellyfin.LiveTv.Guide.GuideManager: Refreshing guide with 7 days of guide data
[2025-06-22 20:28:06.038 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Refresh Guide" Completed after 0 minute(s) and 3 seconds
[2025-06-22 20:29:03.655 +02:00] [INF] [19] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-22 20:29:03.656 +02:00] [INF] [19] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/PELIS"
[2025-06-22 20:29:03.658 +02:00] [INF] [19] Emby.Server.Implementations.IO.LibraryMonitor: Stopping directory watching for path "/CONTENIDO/SERIES"
[2025-06-22 20:29:03.659 +02:00] [INF] [19] Emby.Server.Implementations.Library.LibraryManager: Validating media library
[2025-06-22 20:29:04.718 +02:00] [WRN] [19] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-22 20:29:05.422 +02:00] [WRN] [72] MediaBrowser.Controller.Entities.BaseItem: Library folder "/config/data/playlists" is inaccessible or empty, skipping
[2025-06-22 20:29:05.912 +02:00] [INF] [91] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Destino final 5", Path: "/CONTENIDO/PELIS/Final Destination 5 (2011)/Destino.final.5.(2011).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv", Id: 36f54764-095e-94d0-3e92-b74d94e38234
[2025-06-22 20:29:06.014 +02:00] [INF] [91] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Destino final 4", Path: "/CONTENIDO/PELIS/Destino Final 4/Destino.final.4.(2009).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv", Id: 3aafc93e-8b0d-606a-1292-126439445560
[2025-06-22 20:29:06.113 +02:00] [INF] [91] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Destino final", Path: "/CONTENIDO/PELIS/Final Destination (2000)/Destino.final.(2000).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv", Id: 789b5d16-fd14-1587-eecb-8737c042bea0
[2025-06-22 20:29:06.211 +02:00] [INF] [91] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Destino final 2", Path: "/CONTENIDO/PELIS/Final Destination 2 (2003)/Destino.final.2.(2003).(Spanish.English.Spanishsub).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv", Id: 32992445-e795-f1ae-87b9-a3bdf1c381e5
[2025-06-22 20:29:06.403 +02:00] [INF] [91] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Movie", Name: "Destino final 3", Path: "/CONTENIDO/PELIS/Final Destination 3 (2006)/Destino.final.3.(2006).(Spanish.English.Subs).BDRip.1080p.x264-AC3.by.mokesky.(hispashare.com).mkv", Id: 857fca18-b87e-93ca-2d1c-c98f7b91f53d
[2025-06-22 20:29:06.860 +02:00] [INF] [54] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escaneo de segmentos de medios" Completed after 0 minute(s) and 3 seconds
[2025-06-22 20:29:07.382 +02:00] [INF] [74] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "/CONTENIDO/SERIES/Breaking Bad/Season 6", Id: f44cd26a-0146-e958-3047-fca4ff3a252d
[2025-06-22 20:29:08.322 +02:00] [INF] [72] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-22 20:29:08.322 +02:00] [INF] [72] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 20:29:08.565 +02:00] [ERR] [93] MediaBrowser.Providers.Manager.ProviderManager: "TvdbSeasonImageProvider" failed in GetImageInfos for type "Season" at null
HTTP Response: 

{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}


Tvdb.Sdk.SeasonsException: Invalid seasons id

Status: 400
Response: 
{"status":"failure","message":"InvalidValueType: cannot make item path","data":null}

   at Jellyfin.Plugin.Tvdb.SeasonClient.ExtendedSeasonClient.GetSeasonExtendedWithTranslationsAsync(Double id, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.TvdbClientManager.GetSeasonByIdAsync(Int32 seasonTvdbId, String language, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetSeasonArtworks(Int32 seriesTvdbId, Int32 seasonNumber, String displayOrder, CancellationToken cancellationToken)
   at Jellyfin.Plugin.Tvdb.Providers.TvdbSeasonImageProvider.GetImages(BaseItem item, CancellationToken cancellationToken)
   at MediaBrowser.Providers.Manager.ProviderManager.GetImages(BaseItem item, IRemoteImageProvider provider, String preferredLanguage, Boolean includeAllLanguages, CancellationToken cancellationToken, Nullable`1 type)
[2025-06-22 20:29:08.952 +02:00] [INF] [72] MediaBrowser.Providers.TV.SeriesMetadataService: Removing virtual season 6 in series "Breaking Bad"
[2025-06-22 20:29:08.952 +02:00] [INF] [72] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Season", Name: "Temporada 6", Path: "", Id: 61d62a11-a1cb-1123-6220-dd9fde133dd1
[2025-06-22 20:29:16.494 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: Timer elapsed - processing queued items
[2025-06-22 20:29:16.494 +02:00] [INF] [31] Trakt.Helpers.LibraryManagerEventsHelper: No events... stopping queue timer
[2025-06-22 20:29:24.867 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "a6cab75d69cd163740d236d8cee2389c" "DreamWorks"
[2025-06-22 20:29:24.867 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "DreamWorks", Path: "/config/metadata/Studio/DreamWorks", Id: a6cab75d-69cd-1637-40d2-36d8cee2389c
[2025-06-22 20:29:24.881 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "c58b6cf70231bf00498624105f5522af" "FlipZide"
[2025-06-22 20:29:24.881 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "FlipZide", Path: "/config/metadata/Studio/FlipZide", Id: c58b6cf7-0231-bf00-4986-24105f5522af
[2025-06-22 20:29:24.893 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "c50a1019040a1d767c44d28103b69901" "Hard Eight Pictures"
[2025-06-22 20:29:24.893 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Hard Eight Pictures", Path: "/config/metadata/Studio/Hard Eight Pictures", Id: c50a1019-040a-1d76-7c44-d28103b69901
[2025-06-22 20:29:24.902 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "efdce71681ec138428782294196cf706" "Marc Platt Productions"
[2025-06-22 20:29:24.902 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Marc Platt Productions", Path: "/config/metadata/Studio/Marc Platt Productions", Id: efdce716-81ec-1384-2878-2294196cf706
[2025-06-22 20:29:24.911 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "f4343f7969ad1bce2a3bf10658858e67" "Matinee Pictures"
[2025-06-22 20:29:24.911 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Matinee Pictures", Path: "/config/metadata/Studio/Matinee Pictures", Id: f4343f79-69ad-1bce-2a3b-f10658858e67
[2025-06-22 20:29:24.920 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "1d8bb113b3b16a4ff8abaf41427708bd" "Parallel Zide"
[2025-06-22 20:29:24.920 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Parallel Zide", Path: "/config/metadata/Studio/Parallel Zide", Id: 1d8bb113-b3b1-6a4f-f8ab-af41427708bd
[2025-06-22 20:29:24.929 +02:00] [INF] [37] Emby.Server.Implementations.Library.Validators.StudiosValidator: Deleting dead "Studio" "2fec3af4424aa8351d1612f736837095" "Zide-Perry Productions"
[2025-06-22 20:29:24.929 +02:00] [INF] [37] Emby.Server.Implementations.Library.LibraryManager: Removing item, Type: "Studio", Name: "Zide-Perry Productions", Path: "/config/metadata/Studio/Zide-Perry Productions", Id: 2fec3af4-424a-a835-1d16-12f736837095
[2025-06-22 20:29:25.626 +02:00] [INF] [37] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Escanear la biblioteca" Completed after 0 minute(s) and 21 seconds
[2025-06-22 20:29:25.680 +02:00] [INF] [54] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/Descargas/Colección OLE Mortadelo y Filemón [CBR 188 Comics] + Lector Comic"
[2025-06-22 20:29:25.781 +02:00] [INF] [31] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-06-22 20:29:25.965 +02:00] [INF] [26] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-06-22 21:12:10.925 +02:00] [INF] [23] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 21:12:40.921 +02:00] [WRN] [81] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 21:12:40.926 +02:00] [INF] [81] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 21:15:05.881 +02:00] [INF] [35] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" request
[2025-06-22 21:18:52.931 +02:00] [INF] [76] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 21:19:04.930 +02:00] [INF] [76] Emby.Server.Implementations.Session.SessionWebSocketListener: Sending ForceKeepAlive message to 1 inactive WebSockets.
[2025-06-22 21:19:12.056 +02:00] [WRN] [28] Emby.Server.Implementations.HttpServer.WebSocketConnection: WS "**********" error receiving data: "The remote party closed the WebSocket connection without completing the close handshake."
[2025-06-22 21:19:12.060 +02:00] [INF] [28] Emby.Server.Implementations.HttpServer.WebSocketManager: WS "**********" closed
[2025-06-22 22:07:43.693 +02:00] [INF] [103] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 22:07:43.693 +02:00] [INF] [103] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 22:07:43.695 +02:00] [INF] [103] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-06-22 22:32:46.374 +02:00] [INF] [54] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 22:32:46.374 +02:00] [INF] [54] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-06-22 22:32:46.374 +02:00] [INF] [54] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
