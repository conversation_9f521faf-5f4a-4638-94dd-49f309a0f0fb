{"Protocol":0,"Id":"14857cd781a7139ab1db7a466b13d843","Path":"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv","EncoderPath":null,"EncoderProtocol":null,"Type":0,"Container":"mkv","Size":34844647663,"Name":"How to Train Your Dragon 2 (2014) Remux-2160p","IsRemote":false,"ETag":"74c318e50ca08b9347dd37485593860d","RunTimeTicks":61146310000,"ReadAtNativeFramerate":false,"IgnoreDts":false,"IgnoreIndex":false,"GenPtsInput":false,"SupportsTranscoding":true,"SupportsDirectStream":true,"SupportsDirectPlay":true,"IsInfiniteStream":false,"UseMostCompatibleTranscodingProfile":false,"RequiresOpening":false,"OpenToken":null,"RequiresClosing":false,"LiveStreamId":null,"BufferMs":null,"RequiresLooping":false,"SupportsProbing":true,"VideoType":0,"IsoType":null,"Video3DFormat":null,"MediaStreams":[{"Codec":"hevc","CodecTag":null,"Language":null,"ColorRange":null,"ColorSpace":"bt2020nc","ColorTransfer":"smpte2084","ColorPrimaries":"bt2020","DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":2,"VideoRangeType":2,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":null,"LocalizedForced":null,"LocalizedExternal":null,"LocalizedHearingImpaired":null,"DisplayTitle":"4K HEVC HDR","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":45588553,"BitDepth":10,"RefFrames":1,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":false,"IsHearingImpaired":false,"Height":2160,"Width":3840,"AverageFrameRate":23.976025,"RealFrameRate":23.976025,"ReferenceFrameRate":23.976025,"Profile":"Main 10","Type":1,"AspectRatio":"16:9","Index":0,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":"yuv420p10le","Level":153,"IsAnamorphic":false},{"Codec":"dts","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - DTS - 5.1 - Predeterminado","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":768000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS","Type":0,"AspectRatio":null,"Index":1,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"ac3","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"Spanish - Dolby Digital - 5.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"5.1","BitRate":640000,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":6,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":0,"AspectRatio":null,"Index":2,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"dts","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":2,"LocalizedUndefined":null,"LocalizedDefault":"Predeterminado","LocalizedForced":null,"LocalizedExternal":"Externo","LocalizedHearingImpaired":null,"DisplayTitle":"English - DTS-HD MA \u002B DTS:X - 7.1","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":"7.1","BitRate":6552825,"BitDepth":24,"RefFrames":null,"PacketLength":null,"Channels":8,"SampleRate":48000,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":null,"Width":null,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":"DTS-HD MA \u002B DTS:X","Type":0,"AspectRatio":null,"Index":3,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":false,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":"Forzados","VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Forzados - Spanish - Predeterminado - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":true,"IsForced":true,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":4,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"subrip","CodecTag":null,"Language":"spa","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"Spanish - SUBRIP","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":0,"Width":0,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":5,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":true,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null},{"Codec":"PGSSUB","CodecTag":null,"Language":"eng","ColorRange":null,"ColorSpace":null,"ColorTransfer":null,"ColorPrimaries":null,"DvVersionMajor":null,"DvVersionMinor":null,"DvProfile":null,"DvLevel":null,"RpuPresentFlag":null,"ElPresentFlag":null,"BlPresentFlag":null,"DvBlSignalCompatibilityId":null,"Rotation":null,"Comment":null,"TimeBase":"1/1000","CodecTimeBase":null,"Title":null,"VideoRange":0,"VideoRangeType":0,"VideoDoViTitle":null,"AudioSpatialFormat":0,"LocalizedUndefined":"Indefinido","LocalizedDefault":"Predeterminado","LocalizedForced":"Forzado","LocalizedExternal":"Externo","LocalizedHearingImpaired":"Discapacidad Auditiva","DisplayTitle":"English - PGSSUB","NalLengthSize":null,"IsInterlaced":false,"IsAVC":false,"ChannelLayout":null,"BitRate":null,"BitDepth":null,"RefFrames":null,"PacketLength":null,"Channels":null,"SampleRate":null,"IsDefault":false,"IsForced":false,"IsHearingImpaired":false,"Height":1080,"Width":1920,"AverageFrameRate":null,"RealFrameRate":null,"ReferenceFrameRate":null,"Profile":null,"Type":2,"AspectRatio":null,"Index":6,"Score":null,"IsExternal":false,"DeliveryMethod":null,"DeliveryUrl":null,"IsExternalUrl":null,"IsTextSubtitleStream":false,"SupportsExternalStream":true,"Path":null,"PixelFormat":null,"Level":0,"IsAnamorphic":null}],"MediaAttachments":[],"Formats":[],"Bitrate":53549378,"FallbackMaxStreamingBitrate":null,"Timestamp":null,"RequiredHttpHeaders":{},"TranscodingUrl":null,"TranscodingSubProtocol":0,"TranscodingContainer":null,"AnalyzeDurationMs":null,"DefaultAudioStreamIndex":null,"DefaultSubtitleStreamIndex":null,"HasSegments":false}

/usr/lib/jellyfin-ffmpeg/ffmpeg -analyzeduration 200M -probesize 1G  -i file:"/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv" -map_metadata -1 -map_chapters -1 -threads 0 -map 0:0 -map 0:1 -map -0:s -codec:v:0 libx264 -preset veryfast -crf 23 -maxrate 14232000 -bufsize 28464000 -profile:v:0 high -x264opts:0 subme=0:me_range=16:rc_lookahead=10:me=hex:open_gop=0 -force_key_frames:0 "expr:gte(t,n_forced*3)" -sc_threshold:v:0 0 -vf "setparams=color_primaries=bt2020:color_trc=smpte2084:colorspace=bt2020nc,scale=trunc(min(max(iw\,ih*a)\,2560)/2)*2:trunc(ow/a/2)*2,tonemapx=tonemap=bt2390:desat=0:peak=100:t=bt709:m=bt709:p=bt709:format=yuv420p" -codec:a:0 libfdk_aac -ac 6 -ab 640000 -copyts -avoid_negative_ts disabled -max_muxing_queue_size 2048 -f hls -max_delay 5000000 -hls_time 3 -hls_segment_type mpegts -start_number 0 -hls_segment_filename "/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b%d.ts" -hls_playlist_type vod -hls_list_size 0 -y "/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b.m3u8"


ffmpeg version 7.0.2-Jellyfin Copyright (c) 2000-2024 the FFmpeg developers
  built with gcc 12 (Debian 12.2.0-14)
  configuration: --prefix=/usr/lib/jellyfin-ffmpeg --target-os=linux --extra-version=Jellyfin --disable-doc --disable-ffplay --disable-ptx-compression --disable-static --disable-libxcb --disable-sdl2 --disable-xlib --enable-lto=auto --enable-gpl --enable-version3 --enable-shared --enable-gmp --enable-gnutls --enable-chromaprint --enable-opencl --enable-libdrm --enable-libxml2 --enable-libass --enable-libfreetype --enable-libfribidi --enable-libfontconfig --enable-libharfbuzz --enable-libbluray --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libopenmpt --enable-libdav1d --enable-libsvtav1 --enable-libwebp --enable-libvpx --enable-libx264 --enable-libx265 --enable-libzvbi --enable-libzimg --enable-libfdk-aac --arch=amd64 --enable-libshaderc --enable-libplacebo --enable-vulkan --enable-vaapi --enable-amf --enable-libvpl --enable-ffnvcodec --enable-cuda --enable-cuda-llvm --enable-cuvid --enable-nvdec --enable-nvenc
  libavutil      59.  8.100 / 59.  8.100
  libavcodec     61.  3.100 / 61.  3.100
  libavformat    61.  1.100 / 61.  1.100
  libavdevice    61.  1.100 / 61.  1.100
  libavfilter    10.  1.100 / 10.  1.100
  libswscale      8.  1.100 /  8.  1.100
  libswresample   5.  1.100 /  5.  1.100
  libpostproc    58.  1.100 / 58.  1.100
Input #0, matroska,webm, from 'file:/CONTENIDO/PELIS/How to Train Your Dragon 2 (2014)/How to Train Your Dragon 2 (2014) Remux-2160p.mkv':
  Metadata:
    encoder         : libebml v1.3.6 + libmatroska v1.4.9
    creation_time   : 2019-01-10T18:03:34.000000Z
  Duration: 01:41:54.63, start: 0.000000, bitrate: 45588 kb/s
  Chapters:
    Chapter #0:0: start 0.000000, end 211.920022
      Metadata:
        title           : Chapter 01
    Chapter #0:1: start 211.920022, end 324.615956
      Metadata:
        title           : Chapter 02
    Chapter #0:2: start 324.615956, end 469.927778
      Metadata:
        title           : Chapter 03
    Chapter #0:3: start 469.927778, end 619.952667
      Metadata:
        title           : Chapter 04
    Chapter #0:4: start 619.952667, end 800.257778
      Metadata:
        title           : Chapter 05
    Chapter #0:5: start 800.257778, end 1022.980289
      Metadata:
        title           : Chapter 06
    Chapter #0:6: start 1022.980289, end 1205.621067
      Metadata:
        title           : Chapter 07
    Chapter #0:7: start 1205.621067, end 1427.175733
      Metadata:
        title           : Chapter 08
    Chapter #0:8: start 1427.175733, end 1638.386733
      Metadata:
        title           : Chapter 09
    Chapter #0:9: start 1638.386733, end 1765.388622
      Metadata:
        title           : Chapter 10
    Chapter #0:10: start 1765.388622, end 2025.857156
      Metadata:
        title           : Chapter 11
    Chapter #0:11: start 2025.857156, end 2229.393822
      Metadata:
        title           : Chapter 12
    Chapter #0:12: start 2229.393822, end 2350.890200
      Metadata:
        title           : Chapter 13
    Chapter #0:13: start 2350.890200, end 2463.085622
      Metadata:
        title           : Chapter 14
    Chapter #0:14: start 2463.085622, end 2707.913533
      Metadata:
        title           : Chapter 15
    Chapter #0:15: start 2707.913533, end 2840.546022
      Metadata:
        title           : Chapter 16
    Chapter #0:16: start 2840.546022, end 3115.445667
      Metadata:
        title           : Chapter 17
    Chapter #0:17: start 3115.445667, end 3279.651356
      Metadata:
        title           : Chapter 18
    Chapter #0:18: start 3279.651356, end 3379.918200
      Metadata:
        title           : Chapter 19
    Chapter #0:19: start 3379.918200, end 3637.091778
      Metadata:
        title           : Chapter 20
    Chapter #0:20: start 3637.091778, end 3872.702156
      Metadata:
        title           : Chapter 21
    Chapter #0:21: start 3872.702156, end 3998.869867
      Metadata:
        title           : Chapter 22
    Chapter #0:22: start 3998.869867, end 4188.058867
      Metadata:
        title           : Chapter 23
    Chapter #0:23: start 4188.058867, end 4445.023911
      Metadata:
        title           : Chapter 24
    Chapter #0:24: start 4445.023911, end 4719.047667
      Metadata:
        title           : Chapter 25
    Chapter #0:25: start 4719.047667, end 4871.074533
      Metadata:
        title           : Chapter 26
    Chapter #0:26: start 4871.074533, end 4978.974000
      Metadata:
        title           : Chapter 27
    Chapter #0:27: start 4978.974000, end 5073.985578
      Metadata:
        title           : Chapter 28
    Chapter #0:28: start 5073.985578, end 5288.574956
      Metadata:
        title           : Chapter 29
    Chapter #0:29: start 5288.574956, end 5467.420289
      Metadata:
        title           : Chapter 30
    Chapter #0:30: start 5467.420289, end 5545.915356
      Metadata:
        title           : Chapter 31
    Chapter #0:31: start 5545.915356, end 6114.631000
      Metadata:
        title           : Chapter 32
  Stream #0:0: Video: hevc (Main 10), yuv420p10le(tv, bt2020nc/bt2020/smpte2084), 3840x2160 [SAR 1:1 DAR 16:9], 23.98 fps, 23.98 tbr, 1k tbn (default)
      Metadata:
        BPS-eng         : 37576891
        DURATION-eng    : 01:41:54.609000000
        NUMBER_OF_FRAMES-eng: 146604
        NUMBER_OF_BYTES-eng: 28721000090
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:1(spa): Audio: dts (dca) (DTS), 48000 Hz, 5.1(side), fltp, 768 kb/s (default) (forced)
      Metadata:
        BPS-eng         : 767999
        DURATION-eng    : 01:41:54.614000000
        NUMBER_OF_FRAMES-eng: 573245
        NUMBER_OF_BYTES-eng: 587002880
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:2(spa): Audio: ac3, 48000 Hz, 5.1(side), fltp, 640 kb/s
      Metadata:
        BPS-eng         : 640000
        DURATION-eng    : 01:41:54.624000000
        NUMBER_OF_FRAMES-eng: 191082
        NUMBER_OF_BYTES-eng: 489169920
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:3(eng): Audio: dts (dca) (DTS-HD MA + DTS:X), 48000 Hz, 7.1, s32p (24 bit)
      Metadata:
        BPS-eng         : 6552825
        DURATION-eng    : 01:41:54.614000000
        NUMBER_OF_FRAMES-eng: 573245
        NUMBER_OF_BYTES-eng: 5008499676
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:4(spa): Subtitle: subrip (srt) (default) (forced)
      Metadata:
        title           : Forzados
        BPS-eng         : 0
        DURATION-eng    : 00:09:24.813000000
        NUMBER_OF_FRAMES-eng: 2
        NUMBER_OF_BYTES-eng: 46
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:5(spa): Subtitle: subrip (srt)
      Metadata:
        BPS-eng         : 43
        DURATION-eng    : 01:40:34.444000000
        NUMBER_OF_FRAMES-eng: 1146
        NUMBER_OF_BYTES-eng: 33098
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
  Stream #0:6(eng): Subtitle: hdmv_pgs_subtitle (pgssub), 1920x1080
      Metadata:
        BPS-eng         : 45999
        DURATION-eng    : 01:40:44.163000000
        NUMBER_OF_FRAMES-eng: 3870
        NUMBER_OF_BYTES-eng: 34753782
        _STATISTICS_WRITING_APP-eng: mkvmerge v29.0.0 ('Like It Or Not') 64-bit
        _STATISTICS_WRITING_DATE_UTC-eng: 2019-01-10 18:03:34
        _STATISTICS_TAGS-eng: BPS DURATION NUMBER_OF_FRAMES NUMBER_OF_BYTES
[tonemapx @ 0x559c76b95700] Using CPU capabilities: AVX2 FMA3
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (native) -> h264 (libx264))
  Stream #0:1 -> #0:1 (dts (dca) -> aac (libfdk_aac))
Press [q] to stop, [?] for help
[tonemapx @ 0x7fa614012140] Using CPU capabilities: AVX2 FMA3
[libx264 @ 0x559c76b26fc0] using SAR=1/1
[libx264 @ 0x559c76b26fc0] using cpu capabilities: MMX2 SSE2Fast SSSE3 SSE4.2 AVX FMA3 BMI2 AVX2
[libx264 @ 0x559c76b26fc0] profile High, level 5.0, 4:2:0, 8-bit
[libx264 @ 0x559c76b26fc0] 264 - core 164 r3095 baee400 - H.264/MPEG-4 AVC codec - Copyleft 2003-2022 - http://www.videolan.org/x264.html - options: cabac=1 ref=1 deblock=1:0:0 analyse=0x3:0x113 me=hex subme=0 psy=1 psy_rd=1.00:0.00 mixed_ref=0 me_range=16 chroma_me=1 trellis=0 8x8dct=1 cqm=0 deadzone=21,11 fast_pskip=1 chroma_qp_offset=0 threads=18 lookahead_threads=3 sliced_threads=0 nr=0 decimate=1 interlaced=0 bluray_compat=0 constrained_intra=0 bframes=3 b_pyramid=2 b_adapt=1 b_bias=0 direct=1 weightb=1 open_gop=0 weightp=1 keyint=250 keyint_min=23 scenecut=0 intra_refresh=0 rc_lookahead=10 rc=crf mbtree=1 crf=23.0 qcomp=0.60 qpmin=0 qpmax=69 qpstep=4 vbv_maxrate=14232 vbv_bufsize=28464 crf_max=0.0 nal_hrd=none filler=0 ip_ratio=1.40 aq=1:1.00
Output #0, hls, to '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b.m3u8':
  Metadata:
    encoder         : Lavf61.1.100
  Stream #0:0: Video: h264, yuv420p(tv, bt709, progressive), 2560x1440 [SAR 1:1 DAR 16:9], q=2-31, 23.98 fps, 90k tbn (default)
      Metadata:
        encoder         : Lavc61.3.100 libx264
      Side data:
        cpb: bitrate max/min/avg: 14232000/0/0 buffer size: 28464000 vbv_delay: N/A
  Stream #0:1: Audio: aac, 48000 Hz, 5.1, s16, 640 kb/s (default) (forced)
      Metadata:
        encoder         : Lavc61.3.100 libfdk_aac
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=    0 fps=0.0 q=0.0 size=N/A time=N/A bitrate=N/A speed=N/A    
frame=   13 fps=8.7 q=28.0 size=N/A time=00:00:00.00 bitrate=N/A speed=   0x    
frame=   34 fps= 17 q=28.0 size=N/A time=00:00:00.87 bitrate=N/A speed=0.437x    
frame=   55 fps= 22 q=28.0 size=N/A time=00:00:01.75 bitrate=N/A speed=0.699x    
frame=   72 fps= 24 q=28.0 size=N/A time=00:00:02.46 bitrate=N/A speed=0.819x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b0.ts' for writing
frame=   89 fps= 25 q=28.0 size=N/A time=00:00:03.16 bitrate=N/A speed=0.904x    
frame=  105 fps= 26 q=28.0 size=N/A time=00:00:03.83 bitrate=N/A speed=0.958x    
frame=  127 fps= 28 q=28.0 size=N/A time=00:00:04.75 bitrate=N/A speed=1.06x    
frame=  144 fps= 29 q=28.0 size=N/A time=00:00:05.46 bitrate=N/A speed=1.09x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b1.ts' for writing
frame=  160 fps= 29 q=28.0 size=N/A time=00:00:06.13 bitrate=N/A speed=1.11x    
frame=  183 fps= 30 q=28.0 size=N/A time=00:00:07.09 bitrate=N/A speed=1.18x    
frame=  202 fps= 31 q=28.0 size=N/A time=00:00:07.88 bitrate=N/A speed=1.21x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b2.ts' for writing
frame=  221 fps= 32 q=28.0 size=N/A time=00:00:08.71 bitrate=N/A speed=1.24x    
frame=  241 fps= 32 q=28.0 size=N/A time=00:00:09.55 bitrate=N/A speed=1.27x    
frame=  261 fps= 33 q=28.0 size=N/A time=00:00:10.34 bitrate=N/A speed=1.29x    
frame=  276 fps= 32 q=28.0 size=N/A time=00:00:10.96 bitrate=N/A speed=1.29x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b3.ts' for writing
frame=  293 fps= 33 q=28.0 size=N/A time=00:00:11.67 bitrate=N/A speed= 1.3x    
frame=  311 fps= 33 q=28.0 size=N/A time=00:00:12.42 bitrate=N/A speed=1.31x    
frame=  328 fps= 33 q=28.0 size=N/A time=00:00:13.13 bitrate=N/A speed=1.31x    
frame=  344 fps= 33 q=25.0 size=N/A time=00:00:13.80 bitrate=N/A speed=1.31x    
frame=  356 fps= 32 q=28.0 size=N/A time=00:00:14.30 bitrate=N/A speed= 1.3x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b4.ts' for writing
frame=  370 fps= 32 q=28.0 size=N/A time=00:00:14.88 bitrate=N/A speed=1.29x    
frame=  388 fps= 32 q=28.0 size=N/A time=00:00:15.64 bitrate=N/A speed= 1.3x    
frame=  406 fps= 32 q=28.0 size=N/A time=00:00:16.39 bitrate=N/A speed=1.31x    
frame=  421 fps= 32 q=28.0 size=N/A time=00:00:17.01 bitrate=N/A speed=1.31x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b5.ts' for writing
frame=  440 fps= 33 q=28.0 size=N/A time=00:00:17.80 bitrate=N/A speed=1.32x    
frame=  455 fps= 32 q=28.0 size=N/A time=00:00:18.43 bitrate=N/A speed=1.31x    
frame=  471 fps= 32 q=28.0 size=N/A time=00:00:19.10 bitrate=N/A speed=1.31x    
frame=  491 fps= 33 q=28.0 size=N/A time=00:00:19.93 bitrate=N/A speed=1.33x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b6.ts' for writing
frame=  512 fps= 33 q=28.0 size=N/A time=00:00:20.81 bitrate=N/A speed=1.34x    
frame=  526 fps= 33 q=28.0 size=N/A time=00:00:21.39 bitrate=N/A speed=1.33x    
frame=  547 fps= 33 q=28.0 size=N/A time=00:00:22.27 bitrate=N/A speed=1.35x    
frame=  573 fps= 34 q=28.0 size=N/A time=00:00:23.35 bitrate=N/A speed=1.37x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b7.ts' for writing
frame=  605 fps= 34 q=28.0 size=N/A time=00:00:24.69 bitrate=N/A speed=1.41x    
frame=  633 fps= 35 q=28.0 size=N/A time=00:00:25.85 bitrate=N/A speed=1.43x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b8.ts' for writing
frame=  659 fps= 36 q=28.0 size=N/A time=00:00:26.94 bitrate=N/A speed=1.45x    
frame=  687 fps= 36 q=28.0 size=N/A time=00:00:28.11 bitrate=N/A speed=1.48x    
frame=  720 fps= 37 q=28.0 size=N/A time=00:00:29.48 bitrate=N/A speed=1.51x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b9.ts' for writing
frame=  749 fps= 37 q=28.0 size=N/A time=00:00:30.69 bitrate=N/A speed=1.53x    
frame=  775 fps= 38 q=28.0 size=N/A time=00:00:31.78 bitrate=N/A speed=1.55x    
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b10.ts' for writing
frame=  797 fps= 38 q=28.0 size=N/A time=00:00:32.69 bitrate=N/A speed=1.55x    
frame=  824 fps= 38 q=28.0 size=N/A time=00:00:33.82 bitrate=N/A speed=1.57x    

[q] command received. Exiting.

[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b11.ts' for writing
[hls @ 0x559c76b6f200] Opening '/cache/transcodes/8b38e30ec25b67459eb1e58b70ad8a5b12.ts' for writing
[out#0/hls @ 0x559c7c408580] video:17379KiB audio:2954KiB subtitle:0KiB other streams:0KiB global headers:0KiB muxing overhead: unknown
frame=  906 fps= 40 q=-1.0 Lsize=N/A time=00:00:37.24 bitrate=N/A speed=1.65x    
[libx264 @ 0x559c76b26fc0] frame I:13    Avg QP:13.51  size:102342
[libx264 @ 0x559c76b26fc0] frame P:260   Avg QP:18.33  size: 38250
[libx264 @ 0x559c76b26fc0] frame B:633   Avg QP:19.98  size: 10300
[libx264 @ 0x559c76b26fc0] consecutive B-frames:  5.2%  3.3%  5.0% 86.5%
[libx264 @ 0x559c76b26fc0] mb I  I16..4: 59.7% 29.6% 10.7%
[libx264 @ 0x559c76b26fc0] mb P  I16..4: 16.3% 18.4%  1.5%  P16..4: 11.0%  3.7%  1.0%  0.0%  0.0%    skip:48.1%
[libx264 @ 0x559c76b26fc0] mb B  I16..4:  2.0%  2.7%  0.1%  B16..8:  9.9%  2.3%  0.1%  direct: 4.9%  skip:78.1%  L0:40.3% L1:47.4% BI:12.3%
[libx264 @ 0x559c76b26fc0] 8x8 transform intra:50.2% inter:38.3%
[libx264 @ 0x559c76b26fc0] coded y,uvDC,uvAC intra: 31.9% 42.6% 17.7% inter: 2.2% 8.6% 0.4%
[libx264 @ 0x559c76b26fc0] i16 v,h,dc,p: 51% 21% 13% 15%
[libx264 @ 0x559c76b26fc0] i8 v,h,dc,ddl,ddr,vr,hd,vl,hu: 11% 18% 23%  8%  9%  8% 10%  4%  8%
[libx264 @ 0x559c76b26fc0] i4 v,h,dc,ddl,ddr,vr,hd,vl,hu: 16% 27% 11%  7%  9%  8% 10%  3%  8%
[libx264 @ 0x559c76b26fc0] i8c dc,h,v,p: 53% 23% 17%  8%
[libx264 @ 0x559c76b26fc0] Weighted P-Frames: Y:15.0% UV:8.1%
[libx264 @ 0x559c76b26fc0] kb/s:3767.38